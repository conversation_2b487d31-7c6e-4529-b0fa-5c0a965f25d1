// UI管理模块
export class UIManager {
    constructor(gameState) {
        this.gameState = gameState;
    }

    safeUpdateElement(id, property, value) {
        const element = document.getElementById(id);
        if (element) {
            if (property.includes('.')) {
                const props = property.split('.');
                let obj = element;
                for (let i = 0; i < props.length - 1; i++) {
                    obj = obj[props[i]];
                }
                obj[props[props.length - 1]] = value;
            } else {
                element[property] = value;
            }
        }
    }

    updateUI() {
        // 安全更新状态栏
        this.safeUpdateElement('health-bar', 'style.width',
            `${(this.gameState.player.health / this.gameState.player.maxHealth) * 100}%`);
        this.safeUpdateElement('health-text', 'textContent',
            `${Math.floor(this.gameState.player.health)}/${this.gameState.player.maxHealth}`);

        this.safeUpdateElement('hunger-bar', 'style.width',
            `${(this.gameState.player.hunger / this.gameState.player.maxHunger) * 100}%`);
        this.safeUpdateElement('hunger-text', 'textContent',
            `${Math.floor(this.gameState.player.hunger)}/${this.gameState.player.maxHunger}`);

        this.safeUpdateElement('fuel-bar', 'style.width',
            `${(this.gameState.player.fuel / this.gameState.player.maxFuel) * 100}%`);
        this.safeUpdateElement('fuel-text', 'textContent',
            `${Math.floor(this.gameState.player.fuel)}/${this.gameState.player.maxFuel}`);

        this.safeUpdateElement('radiation-bar', 'style.width',
            `${(this.gameState.player.radiation / this.gameState.player.maxRadiation) * 100}%`);
        this.safeUpdateElement('radiation-text', 'textContent',
            `${Math.floor(this.gameState.player.radiation)}/${this.gameState.player.maxRadiation}`);

        this.safeUpdateElement('morale-bar', 'style.width',
            `${(this.gameState.player.morale / this.gameState.player.maxMorale) * 100}%`);
        this.safeUpdateElement('morale-text', 'textContent',
            `${Math.floor(this.gameState.player.morale)}/${this.gameState.player.maxMorale}`);

        // 更新房车状态 - 详细显示升级效果
        this.safeUpdateElement('armor-level', 'textContent',
            `${this.gameState.vehicle.armor}级 (防御+${this.gameState.vehicle.armor * 2})`);

        this.safeUpdateElement('storage-level', 'textContent',
            `${this.gameState.vehicle.maxStorage}kg`);

        this.safeUpdateElement('weapon-level', 'textContent',
            this.gameState.vehicle.weapon > 0 ?
            `${this.gameState.vehicle.weapon}级 (攻击+${this.gameState.vehicle.weapon * 5})` : '无');

        this.safeUpdateElement('engine-level', 'textContent',
            `${this.gameState.vehicle.engine}级 (效率${this.gameState.vehicle.engine > 1 ? '+' + ((this.gameState.vehicle.engine - 1) * 20) + '%' : '标准'})`);

        // 更新新增的系统状态
        this.safeUpdateElement('communication-level', 'textContent',
            this.gameState.vehicle.communication > 0 ? `${this.gameState.vehicle.communication}级` : '无');

        this.safeUpdateElement('medical-level', 'textContent',
            this.gameState.vehicle.medical > 0 ? `${this.gameState.vehicle.medical}级` : '无');

        this.safeUpdateElement('survival-level', 'textContent',
            this.gameState.vehicle.survival > 0 ? `${this.gameState.vehicle.survival}级` : '无');

        this.safeUpdateElement('special-level', 'textContent',
            this.gameState.vehicle.special > 0 ? `${this.gameState.vehicle.special}级` : '无');

        // 更新扩展系统状态
        this.safeUpdateElement('protection-level', 'textContent',
            this.gameState.vehicle.protection > 0 ? `${this.gameState.vehicle.protection}级` : '无');

        this.safeUpdateElement('detection-level', 'textContent',
            this.gameState.vehicle.detection > 0 ? `${this.gameState.vehicle.detection}级` : '无');

        this.safeUpdateElement('power-level', 'textContent',
            this.gameState.vehicle.power > 0 ? `${this.gameState.vehicle.power}级` : '无');

        // 更新位置信息
        this.safeUpdateElement('location-name', 'textContent', this.gameState.location.name);
        this.safeUpdateElement('location-description', 'textContent', this.gameState.location.description);

        // 更新天数和时间
        this.safeUpdateElement('day-counter', 'textContent', this.gameState.day);

        // 更新时间显示
        const hour = this.gameState.hour.toString().padStart(2, '0');
        const minute = this.gameState.minute.toString().padStart(2, '0');
        this.safeUpdateElement('time-display', 'textContent', `${hour}:${minute}`);

        // 更新时间段
        const timeOfDay = this.getTimeOfDay();
        this.safeUpdateElement('time-period', 'textContent', timeOfDay);

        // 更新天气
        const weather = this.getWeatherCondition();
        this.safeUpdateElement('weather-condition', 'textContent', weather.description);
    }

    addLogEntry(message) {
        const logContent = document.getElementById('game-log');
        if (!logContent) return;

        const entry = document.createElement('div');
        entry.className = 'log-entry';
        entry.textContent = `第${this.gameState.day}天: ${message}`;
        logContent.appendChild(entry);
        logContent.scrollTop = logContent.scrollHeight;
    }

    updateMapDisplay(mapSystem) {
        // 检查地图界面是否存在
        const mapGrid = document.getElementById('map-grid');
        if (!mapGrid) {
            return;
        }

        const mapData = mapSystem.getCurrentMapData();
        const currentLoc = this.gameState.map.currentLocation;

        // 更新区域信息
        const regionElement = document.getElementById('current-region');
        const coordsElement = document.getElementById('current-coordinates');
        if (regionElement) regionElement.textContent = mapData.name;
        if (coordsElement) coordsElement.textContent = `(${currentLoc.x}, ${currentLoc.y})`;

        // 生成地图网格
        if (!mapGrid) return;

        mapGrid.innerHTML = '';
        mapGrid.style.gridTemplateColumns = `repeat(${mapData.size}, 1fr)`;

        for (let y = 0; y < mapData.size; y++) {
            for (let x = 0; x < mapData.size; x++) {
                const cell = document.createElement('div');
                cell.className = 'map-cell';

                const locationKey = mapSystem.getLocationKey(x, y);
                const locationData = mapData.locations[locationKey];

                // 设置单元格状态
                if (x === currentLoc.x && y === currentLoc.y) {
                    cell.classList.add('current');
                    cell.textContent = '🚐';
                } else if (mapSystem.isLocationExplored(x, y)) {
                    cell.classList.add('explored');
                    cell.textContent = mapSystem.getLocationIcon(locationData?.type);
                } else if (mapSystem.isLocationKnown(x, y)) {
                    cell.classList.add('known');
                    cell.textContent = '?';
                } else {
                    cell.classList.add('unknown');
                    cell.textContent = '';
                }

                // 添加点击事件
                if (mapSystem.isLocationKnown(x, y)) {
                    cell.style.cursor = 'pointer';
                    cell.onclick = () => window.game.moveToMapLocation(x, y);

                    // 添加悬停提示
                    if (locationData) {
                        cell.title = `${locationData.name}\n点击前往`;
                    }
                }

                mapGrid.appendChild(cell);
            }
        }
    }

    openMapPanel(mapSystem) {
        document.getElementById('game-screen').classList.remove('active');
        document.getElementById('map-screen').classList.add('active');
        // 延迟一帧确保DOM更新完成
        setTimeout(() => this.updateMapDisplay(mapSystem), 0);
    }

    closeMapPanel() {
        document.getElementById('map-screen').classList.remove('active');
        document.getElementById('game-screen').classList.add('active');
    }

    toggleSound(soundManager) {
        const enabled = soundManager.toggle();
        const button = document.getElementById('sound-toggle');
        if (button) {
            button.textContent = enabled ? '🔊 音效开启' : '🔇 音效关闭';
        }
        return enabled;
    }

    // 获取时间段描述
    getTimeOfDay() {
        const hour = this.gameState.hour;
        if (hour >= 6 && hour < 12) return '上午';
        if (hour >= 12 && hour < 18) return '下午';
        if (hour >= 18 && hour < 22) return '傍晚';
        return '深夜';
    }

    // 获取天气状况
    getWeatherCondition() {
        const hour = this.gameState.hour;
        const day = this.gameState.day;

        // 简单的天气模拟
        const weatherSeed = (day * 24 + hour) % 100;

        if (weatherSeed < 60) {
            return { type: 'clear', description: '晴朗' };
        } else if (weatherSeed < 80) {
            return { type: 'cloudy', description: '多云' };
        } else if (weatherSeed < 90) {
            return { type: 'rain', description: '下雨' };
        } else if (weatherSeed < 95) {
            return { type: 'storm', description: '暴风雨' };
        } else {
            return { type: 'fog', description: '大雾' };
        }
    }

    safeUpdateElement(id, property, value) {
        const element = document.getElementById(id);
        if (element) {
            element[property] = value;
        }
    }
}
