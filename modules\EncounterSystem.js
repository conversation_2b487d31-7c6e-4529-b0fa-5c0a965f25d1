// 遭遇系统模块
export class EncounterSystem {
    constructor(gameState, soundManager, timeSystem) {
        this.gameState = gameState;
        this.soundManager = soundManager;
        this.timeSystem = timeSystem;
        this.encounters = this.initializeEncounters();
    }

    initializeEncounters() {
        return {
            // 移动时的遭遇
            travel: [
                {
                    id: 'roadblock',
                    name: '路障',
                    description: '前方道路被废弃车辆堵塞',
                    probability: 0.15,
                    type: 'obstacle',
                    options: [
                        { text: '强行冲过', stat: 'vehicle_speed', difficulty: 7, success: '成功冲破路障', failure: '房车受损' },
                        { text: '绕道而行', stat: 'fuel', cost: 10, success: '安全绕过路障' },
                        { text: '清理路障', stat: 'player_speed', difficulty: 5, time: 30, success: '清理了路障' }
                    ]
                },
                {
                    id: 'chase',
                    name: '追逐战',
                    description: '一群暴徒驾驶改装车追赶你！',
                    probability: 0.1,
                    type: 'chase',
                    options: [
                        { text: '全速逃跑', stat: 'vehicle_speed', difficulty: 8, success: '成功甩掉追兵', failure: '被追上，进入战斗' },
                        { text: '战术机动', stat: 'player_speed', difficulty: 6, success: '巧妙摆脱追击', failure: '机动失败' },
                        { text: '停车迎战', type: 'combat', success: '主动进入战斗' }
                    ]
                },
                {
                    id: 'breakdown',
                    name: '机械故障',
                    description: '房车突然出现机械故障',
                    probability: 0.08,
                    type: 'technical',
                    options: [
                        { text: '紧急修理', stat: 'tools', cost: 1, success: '修好了故障', failure: '修理失败，损失燃料' },
                        { text: '强行启动', stat: 'vehicle_condition', difficulty: 6, success: '勉强启动', failure: '引擎受损' },
                        { text: '等待自然恢复', time: 60, success: '故障自行恢复' }
                    ]
                }
            ],
            
            // 探索时的遭遇
            exploration: [
                {
                    id: 'trap',
                    name: '陷阱',
                    description: '你踩到了一个隐藏的陷阱！',
                    probability: 0.12,
                    type: 'trap',
                    options: [
                        { text: '快速闪避', stat: 'player_speed', difficulty: 7, success: '成功避开陷阱', failure: '受到伤害' },
                        { text: '小心拆除', stat: 'tools', cost: 1, success: '拆除陷阱并获得材料', failure: '拆除失败' },
                        { text: '硬扛伤害', damage: 15, success: '承受伤害继续前进' }
                    ]
                },
                {
                    id: 'survivor',
                    name: '幸存者',
                    description: '你遇到了一个受伤的幸存者',
                    probability: 0.08,
                    type: 'social',
                    options: [
                        { text: '救助他', stat: 'medicine', cost: 2, success: '获得感谢和信息', reward: 'information' },
                        { text: '交易物资', type: 'trade', success: '进行物资交易' },
                        { text: '保持距离', success: '安全离开，但错过机会' }
                    ]
                },
                {
                    id: 'hidden_cache',
                    name: '隐藏储藏',
                    description: '你发现了一个隐藏的物资储藏点',
                    probability: 0.06,
                    type: 'discovery',
                    options: [
                        { text: '仔细搜索', stat: 'player_speed', difficulty: 4, time: 20, success: '发现大量物资', reward: 'resources' },
                        { text: '快速搜刮', time: 5, success: '获得少量物资', reward: 'basic_resources' },
                        { text: '检查陷阱', stat: 'detection', difficulty: 5, success: '安全获得物资', failure: '触发陷阱' }
                    ]
                }
            ],
            
            // 特殊时间遭遇
            special: [
                {
                    id: 'night_stalker',
                    name: '夜行者',
                    description: '深夜中，你听到了奇怪的声音...',
                    probability: 0.2,
                    timeCondition: 'night',
                    type: 'horror',
                    options: [
                        { text: '保持警惕', stat: 'detection', difficulty: 6, success: '发现威胁并避开', failure: '被偷袭' },
                        { text: '开灯驱赶', stat: 'power', cost: 1, success: '光线驱散了威胁' },
                        { text: '躲藏等待', stat: 'player_speed', difficulty: 5, time: 30, success: '安全度过' }
                    ]
                },
                {
                    id: 'radiation_storm',
                    name: '辐射风暴',
                    description: '天空中出现了绿色的辐射云',
                    probability: 0.1,
                    weatherCondition: 'storm',
                    type: 'environmental',
                    options: [
                        { text: '寻找掩护', stat: 'survival', difficulty: 6, success: '找到安全地点', failure: '受到辐射' },
                        { text: '快速通过', stat: 'vehicle_speed', difficulty: 8, success: '冲出风暴区', failure: '被困在风暴中' },
                        { text: '使用防护', stat: 'filters', cost: 2, success: '防护设备保护了你' }
                    ]
                }
            ]
        };
    }

    // 检查是否触发遭遇
    checkEncounter(activityType) {
        // 冷却时间检查
        const currentTime = this.gameState.day * 24 + this.gameState.hour;
        if (currentTime - this.gameState.lastEncounterTime < this.gameState.encounterCooldown) {
            return null;
        }

        const encounterTypes = this.encounters[activityType] || [];
        const timeEffects = this.timeSystem.getTimeEffects();
        const dangerModifier = this.timeSystem.getDangerModifier();

        for (const encounter of encounterTypes) {
            let probability = encounter.probability * dangerModifier;

            // 检查特殊条件
            if (encounter.timeCondition) {
                if (encounter.timeCondition === 'night' && (this.gameState.hour < 6 || this.gameState.hour >= 22)) {
                    probability *= 2;
                } else if (encounter.timeCondition !== 'night') {
                    continue;
                }
            }

            if (encounter.weatherCondition) {
                const weather = this.timeSystem.getWeatherCondition();
                if (weather.type !== encounter.weatherCondition) {
                    continue;
                }
                probability *= 1.5;
            }

            // 房车状态影响
            if (this.gameState.overweight) {
                probability *= 1.3; // 超重增加遭遇概率
            }

            if (Math.random() < probability) {
                this.gameState.lastEncounterTime = currentTime;
                this.gameState.encounterCooldown = 2 + Math.random() * 4; // 2-6小时冷却
                return encounter;
            }
        }

        return null;
    }

    // 处理遭遇选择
    handleEncounterChoice(encounter, choiceIndex, addLogEntry, updateUI) {
        const choice = encounter.options[choiceIndex];
        if (!choice) return false;

        let success = true;
        let result = choice.success || '行动完成';

        // 处理不同类型的选择
        if (choice.stat) {
            success = this.checkStatTest(choice.stat, choice.difficulty || 5);
            result = success ? choice.success : choice.failure;
        }

        if (choice.cost && choice.stat) {
            // 消耗资源
            if (choice.stat === 'fuel') {
                if (this.gameState.player.fuel >= choice.cost) {
                    this.gameState.player.fuel -= choice.cost;
                } else {
                    success = false;
                    result = '燃料不足！';
                }
            } else if (this.gameState.inventory[choice.stat] >= choice.cost) {
                this.gameState.inventory[choice.stat] -= choice.cost;
            } else {
                success = false;
                result = `${choice.stat}不足！`;
            }
        }

        if (choice.damage) {
            this.gameState.player.health = Math.max(0, this.gameState.player.health - choice.damage);
        }

        if (choice.time) {
            this.timeSystem.advanceTime(choice.time);
        }

        // 处理奖励
        if (success && choice.reward) {
            this.giveReward(choice.reward, addLogEntry);
        }

        // 处理失败后果
        if (!success) {
            this.handleFailure(encounter, choice, addLogEntry);
        }

        addLogEntry(`遭遇结果: ${result}`);
        updateUI();
        return success;
    }

    // 检查属性测试
    checkStatTest(stat, difficulty) {
        let statValue = 5; // 默认值

        switch (stat) {
            case 'player_speed':
                statValue = this.gameState.player.speed;
                break;
            case 'vehicle_speed':
                statValue = this.gameState.vehicle.speed;
                break;
            case 'vehicle_condition':
                statValue = this.gameState.vehicle.condition / 10;
                break;
            case 'detection':
                statValue = this.gameState.vehicle.detection * 2 + 3;
                break;
            case 'survival':
                statValue = this.gameState.vehicle.survival * 2 + 3;
                break;
            case 'power':
                statValue = this.gameState.vehicle.power * 2 + 3;
                break;
            default:
                statValue = 5;
        }

        // 添加随机因素
        const roll = Math.random() * 10 + 1;
        return (statValue + roll) >= difficulty;
    }

    // 给予奖励
    giveReward(rewardType, addLogEntry) {
        switch (rewardType) {
            case 'resources':
                this.gameState.inventory.scrap = (this.gameState.inventory.scrap || 0) + 3;
                this.gameState.inventory.fuel = (this.gameState.inventory.fuel || 0) + 5;
                addLogEntry('获得了废料×3和燃料×5');
                break;
            case 'basic_resources':
                this.gameState.inventory.scrap = (this.gameState.inventory.scrap || 0) + 1;
                addLogEntry('获得了废料×1');
                break;
            case 'information':
                // 揭示周围地图
                addLogEntry('获得了周围地区的情报');
                break;
        }
    }

    // 处理失败后果
    handleFailure(encounter, choice, addLogEntry) {
        switch (encounter.type) {
            case 'chase':
                if (choice.failure === '被追上，进入战斗') {
                    // 触发战斗
                    addLogEntry('被暴徒追上了！准备战斗！');
                }
                break;
            case 'trap':
                this.gameState.player.health = Math.max(0, this.gameState.player.health - 10);
                addLogEntry('陷阱造成了伤害！');
                break;
            case 'technical':
                this.gameState.player.fuel = Math.max(0, this.gameState.player.fuel - 5);
                this.gameState.vehicle.condition = Math.max(0, this.gameState.vehicle.condition - 10);
                addLogEntry('机械故障导致燃料泄漏和车辆损坏！');
                break;
        }
    }

    // 获取遭遇概率修正
    getEncounterModifiers() {
        let modifier = 1.0;

        // 时间修正
        modifier *= this.timeSystem.getDangerModifier();

        // 负重修正
        if (this.gameState.overweight) {
            modifier *= 1.3;
        }

        // 房车状态修正
        if (this.gameState.vehicle.condition < 50) {
            modifier *= 1.2;
        }

        // 探测系统减少遭遇
        if (this.gameState.vehicle.detection > 0) {
            modifier *= (1 - this.gameState.vehicle.detection * 0.1);
        }

        return modifier;
    }

    // 生成遭遇描述
    generateEncounterDescription(encounter) {
        let description = encounter.description;

        // 根据时间添加氛围描述
        const timeOfDay = this.timeSystem.getTimeOfDay();
        const weather = this.timeSystem.getWeatherCondition();

        if (timeOfDay === '深夜') {
            description += ' 夜色中的危险更加难以预料...';
        }

        if (weather.type !== 'clear') {
            description += ` ${weather.description}让情况变得更加复杂。`;
        }

        return description;
    }
}
