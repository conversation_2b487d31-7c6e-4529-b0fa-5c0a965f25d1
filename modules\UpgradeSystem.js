// 升级系统模块
export class UpgradeSystem {
    constructor(gameState, soundManager, inventorySystem) {
        this.gameState = gameState;
        this.soundManager = soundManager;
        this.inventorySystem = inventorySystem;
        this.upgrades = this.initializeUpgrades();
    }

    initializeUpgrades() {
        return {
            armor: [
                { level: 2, cost: { scrap: 5 }, name: "钢板装甲", description: "增加基础防护" },
                { level: 3, cost: { scrap: 10, tools: 1 }, name: "复合装甲", description: "大幅提升防护能力" },
                { level: 4, cost: { scrap: 15, rare_metals: 2 }, name: "合金装甲", description: "使用稀有金属制造的高级装甲" },
                { level: 5, cost: { scrap: 25, rare_metals: 5, blueprints: 1 }, name: "纳米装甲", description: "最先进的防护技术" }
            ],
            storage: [
                { level: 75, cost: { scrap: 3 }, name: "货架扩展", description: "增加25kg载重" },
                { level: 100, cost: { scrap: 8, tools: 1 }, name: "拖车改装", description: "大幅增加载重" },
                { level: 150, cost: { scrap: 15, electronics: 2 }, name: "智能储存", description: "电子化管理储存空间" },
                { level: 200, cost: { scrap: 25, rare_metals: 3, blueprints: 1 }, name: "空间压缩", description: "超大储存容量" }
            ],
            weapon: [
                { level: 1, cost: { scrap: 4, weapon_parts: 1 }, name: "车载机枪", description: "基础武器系统" },
                { level: 2, cost: { scrap: 8, weapon_parts: 2 }, name: "重机枪", description: "强力武器系统" },
                { level: 3, cost: { scrap: 15, weapon_parts: 3, electronics: 1 }, name: "激光炮", description: "高科技能量武器" },
                { level: 4, cost: { scrap: 25, weapon_parts: 5, rare_metals: 2 }, name: "等离子炮", description: "毁灭性武器系统" },
                { level: 5, cost: { weapon_parts: 8, rare_metals: 5, blueprints: 2 }, name: "反物质炮", description: "终极武器" }
            ],
            engine: [
                { level: 2, cost: { scrap: 6, fuel: 10 }, name: "涡轮增压", description: "提升燃油效率" },
                { level: 3, cost: { scrap: 12, tools: 1 }, name: "混合动力", description: "大幅提升效率" },
                { level: 4, cost: { scrap: 20, electronics: 2, batteries: 3 }, name: "电动引擎", description: "清洁高效的电力驱动" },
                { level: 5, cost: { rare_metals: 3, electronics: 5, blueprints: 1 }, name: "核动力", description: "几乎无限的能源" }
            ],
            communication: [
                { level: 1, cost: { electronics: 2, batteries: 1 }, name: "无线电台", description: "基础通讯设备" },
                { level: 2, cost: { electronics: 4, batteries: 2, scrap: 5 }, name: "卫星通讯", description: "远程通讯能力" },
                { level: 3, cost: { electronics: 8, rare_metals: 2, blueprints: 1 }, name: "量子通讯", description: "即时全球通讯" }
            ],
            medical: [
                { level: 1, cost: { medicine: 3, electronics: 1 }, name: "医疗包", description: "基础医疗设备" },
                { level: 2, cost: { medicine: 6, electronics: 2, chemicals: 2 }, name: "医疗舱", description: "自动治疗系统" },
                { level: 3, cost: { medicine: 10, electronics: 5, rare_metals: 2 }, name: "再生舱", description: "高级生命维持系统" }
            ],
            survival: [
                { level: 1, cost: { filters: 2, chemicals: 1 }, name: "空气净化", description: "减少辐射伤害" },
                { level: 2, cost: { filters: 4, electronics: 2, chemicals: 3 }, name: "环境控制", description: "完全隔离外部环境" },
                { level: 3, cost: { filters: 8, electronics: 4, rare_metals: 2 }, name: "生态循环", description: "自给自足的生存系统" }
            ],
            special: [
                { level: 1, cost: { blueprints: 2, electronics: 5, rare_metals: 3 }, name: "隐形装置", description: "降低被发现概率，减少战斗遭遇" },
                { level: 2, cost: { blueprints: 3, electronics: 8, rare_metals: 5 }, name: "时间扭曲", description: "加速时间流逝，快速恢复状态" },
                { level: 3, cost: { blueprints: 5, rare_metals: 10, chemicals: 8 }, name: "维度跳跃", description: "紧急传送到安全地点" },
                { level: 4, cost: { blueprints: 8, rare_metals: 15, electronics: 12 }, name: "能量护盾", description: "吸收伤害的能量屏障" },
                { level: 5, cost: { blueprints: 10, rare_metals: 20, chemicals: 15 }, name: "物质重组", description: "转换物资类型的终极科技" }
            ],

            // 新增：防护系统
            protection: [
                { level: 1, cost: { scrap: 8, electronics: 2 }, name: "基础护盾", description: "提供额外防护" },
                { level: 2, cost: { scrap: 15, electronics: 4, rare_metals: 1 }, name: "能量护盾", description: "能量防护系统" },
                { level: 3, cost: { scrap: 25, electronics: 8, rare_metals: 3 }, name: "反应装甲", description: "主动防御系统" }
            ],

            // 新增：探测系统
            detection: [
                { level: 1, cost: { electronics: 3, batteries: 2 }, name: "雷达系统", description: "探测周围敌人和资源" },
                { level: 2, cost: { electronics: 6, batteries: 4, rare_metals: 1 }, name: "生命探测", description: "探测生物信号" },
                { level: 3, cost: { electronics: 10, batteries: 8, rare_metals: 3 }, name: "量子扫描", description: "全方位环境分析" }
            ],

            // 新增：动力系统
            power: [
                { level: 1, cost: { batteries: 5, electronics: 3 }, name: "太阳能板", description: "可再生能源系统" },
                { level: 2, cost: { batteries: 10, electronics: 6, rare_metals: 2 }, name: "核电池", description: "长效能源供应" },
                { level: 3, cost: { batteries: 15, electronics: 12, rare_metals: 5 }, name: "反物质核心", description: "无限能源系统" }
            ]
        };
    }

    generateUpgradeOptions() {
        for (const [category, options] of Object.entries(this.upgrades)) {
            const container = document.getElementById(`${category}-upgrades`);
            if (!container) continue;

            container.innerHTML = '';

            for (const upgrade of options) {
                const currentLevel = this.gameState.vehicle[category];
                if (upgrade.level > currentLevel) {
                    const canAfford = Object.entries(upgrade.cost).every(([item, amount]) =>
                        (this.gameState.inventory[item] || 0) >= amount);

                    const upgradeDiv = document.createElement('div');
                    upgradeDiv.className = `upgrade-option ${canAfford ? 'affordable' : 'expensive'}`;
                    upgradeDiv.innerHTML = `
                        <h4>${upgrade.name}</h4>
                        <p>${upgrade.description}</p>
                        <div class="cost">需要: ${Object.entries(upgrade.cost).map(([item, amount]) =>
                            `${this.inventorySystem.getItemName(item)} ×${amount}`).join(', ')}</div>
                        <button onclick="window.game.purchaseUpgrade('${category}', ${upgrade.level}, ${JSON.stringify(upgrade.cost).replace(/"/g, '&quot;')})"
                                ${canAfford ? '' : 'disabled'}>升级</button>
                    `;
                    container.appendChild(upgradeDiv);
                    break; // 只显示下一级升级
                }
            }
        }
    }

    purchaseUpgrade(category, level, cost, addLogEntry, updateUI, updateInventory) {
        const costObj = typeof cost === 'string' ? JSON.parse(cost) : cost;

        // 检查是否有足够资源
        for (const [item, amount] of Object.entries(costObj)) {
            if ((this.gameState.inventory[item] || 0) < amount) {
                addLogEntry("资源不足，无法升级！");
                return;
            }
        }

        // 扣除资源
        for (const [item, amount] of Object.entries(costObj)) {
            this.gameState.inventory[item] -= amount;
        }

        // 应用升级
        if (category === 'storage') {
            this.gameState.vehicle.maxStorage = level;
        } else {
            this.gameState.vehicle[category] = level;
        }

        this.soundManager.play('upgrade');
        addLogEntry(`成功升级了${this.getCategoryName(category)}！`);
        updateUI();
        updateInventory();
        this.generateUpgradeOptions();
    }

    getCategoryName(category) {
        const names = {
            armor: '装甲',
            storage: '储存空间',
            weapon: '武器系统',
            engine: '引擎',
            communication: '通讯系统',
            medical: '医疗系统',
            survival: '生存系统',
            special: '特殊装备'
        };
        return names[category] || category;
    }

    openUpgradePanel() {
        document.getElementById('game-screen').classList.remove('active');
        document.getElementById('upgrade-screen').classList.add('active');
        this.generateUpgradeOptions();
    }

    closeUpgradePanel() {
        document.getElementById('upgrade-screen').classList.remove('active');
        document.getElementById('game-screen').classList.add('active');
    }
}
