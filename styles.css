/* 末日房车游戏样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Courier New', monospace;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 20, 20, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(20, 20, 120, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #2c1810 0%, #1a0f0a 100%);
    color: #e0d5c7;
    min-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
}

/* 末日风格背景纹理 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 2px,
            rgba(0, 0, 0, 0.03) 2px,
            rgba(0, 0, 0, 0.03) 4px
        ),
        repeating-linear-gradient(
            0deg,
            transparent,
            transparent 2px,
            rgba(0, 0, 0, 0.03) 2px,
            rgba(0, 0, 0, 0.03) 4px
        );
    pointer-events: none;
    z-index: -1;
}

/* 动画关键帧 */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
    50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.6); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

#game-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 15px;
    min-height: 100vh;
    animation: fadeIn 0.8s ease-out;
}

/* 游戏标题 */
.game-header {
    text-align: center;
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #8b4513;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(139, 69, 19, 0.3);
}

.game-header h1 {
    font-size: 3em;
    color: #ff6b35;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    margin-bottom: 10px;
}

.subtitle {
    font-size: 1.2em;
    color: #d4af37;
    font-style: italic;
}

/* 时间显示系统 */
.time-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.day-counter, .time-counter, .weather-display {
    font-size: 1em;
    color: #e0d5c7;
    background: rgba(0, 0, 0, 0.4);
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.time-counter {
    display: flex;
    align-items: center;
    gap: 8px;
}

#time-display {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #ff6b35;
    text-shadow: 0 0 5px rgba(255, 107, 53, 0.5);
    font-size: 1.1em;
}

#time-period {
    color: #d4af37;
    font-size: 0.9em;
}

.weather-display {
    color: #87ceeb;
}

/* 头部控制按钮 */
.header-controls {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 8px;
}

.memory-btn, .crafting-btn, .sound-toggle {
    background: linear-gradient(135deg, #654321, #8b4513);
    color: #fff;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-family: inherit;
    transition: all 0.3s ease;
    font-size: 0.85em;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.memory-btn:hover, .crafting-btn:hover, .sound-toggle:hover {
    background: linear-gradient(135deg, #8b4513, #a0522d);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* 负重警告系统 */
.weight-warning {
    margin-top: 8px;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 0.85em;
    text-align: center;
    font-weight: bold;
    animation: pulse 2s infinite;
}

.weight-warning.low {
    background: rgba(255, 235, 59, 0.2);
    border: 1px solid #ffeb3b;
    color: #ffeb3b;
}

.weight-warning.medium {
    background: rgba(255, 152, 0, 0.2);
    border: 1px solid #ff9800;
    color: #ff9800;
}

.weight-warning.high {
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid #f44336;
    color: #f44336;
}

.weight-warning.critical {
    background: rgba(211, 47, 47, 0.3);
    border: 1px solid #d32f2f;
    color: #d32f2f;
    animation: shake 1s infinite;
}

.vehicle-condition {
    margin-top: 5px;
    font-size: 0.8em;
    text-align: center;
    padding: 4px 8px;
    border-radius: 4px;
}

.vehicle-condition.excellent {
    color: #4caf50;
    background: rgba(76, 175, 80, 0.1);
}

.vehicle-condition.good {
    color: #ffeb3b;
    background: rgba(255, 235, 59, 0.1);
}

.vehicle-condition.poor {
    color: #ff9800;
    background: rgba(255, 152, 0, 0.1);
}

.vehicle-condition.critical {
    color: #f44336;
    background: rgba(244, 67, 54, 0.1);
}

/* 屏幕切换 */
.screen {
    display: none;
}

.screen.active {
    display: block;
}

/* 状态栏 */
.status-bar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    background: rgba(0, 0, 0, 0.8);
    padding: 15px;
    border: 2px solid #654321;
    border-radius: 8px;
    margin-bottom: 20px;
    animation: slideIn 0.6s ease-out;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-label {
    font-weight: bold;
    color: #d4af37;
    min-width: 80px;
}

.status-bar-container {
    width: 150px;
    height: 20px;
    background: #333;
    border: 1px solid #666;
    border-radius: 10px;
    overflow: hidden;
}

.status-bar-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.status-bar-fill.health {
    background: linear-gradient(90deg, #ff4444, #ff6666);
    width: 100%;
}

.status-bar-fill.hunger {
    background: linear-gradient(90deg, #ff8800, #ffaa00);
    width: 100%;
}

.status-bar-fill.fuel {
    background: linear-gradient(90deg, #0088ff, #00aaff);
    width: 50%;
}

.status-bar-fill.radiation {
    background: linear-gradient(90deg, #ff0080, #ff4080);
    width: 0%;
}

.status-bar-fill.morale {
    background: linear-gradient(90deg, #8a2be2, #9370db);
    width: 100%;
}

/* 主游戏区域 */
.main-game-area {
    display: grid;
    grid-template-columns: 1fr 1.5fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
    min-height: 0;
}

/* 面板通用样式 */
.vehicle-panel, .exploration-panel, .inventory-panel {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #8b4513;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 0 15px rgba(139, 69, 19, 0.2);
    min-height: 0;
    max-height: 70vh;
    overflow-y: auto;
    animation: fadeIn 0.8s ease-out;
}

.vehicle-panel h3, .exploration-panel h3, .inventory-panel h3 {
    color: #ff6b35;
    margin-bottom: 15px;
    text-align: center;
    font-size: 1.3em;
    border-bottom: 1px solid #654321;
    padding-bottom: 10px;
}

/* 房车面板 */
.vehicle-display {
    text-align: center;
    margin-bottom: 20px;
}

.vehicle-image {
    font-size: 4em;
    margin-bottom: 15px;
    filter: sepia(1) hue-rotate(20deg);
}

.animated-vehicle {
    animation: pulse 2s infinite;
    transition: all 0.3s ease;
}

.animated-vehicle:hover {
    animation: shake 0.5s ease-in-out;
    transform: scale(1.1);
}

.vehicle-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat-group {
    background: rgba(139, 69, 19, 0.1);
    border-radius: 8px;
    padding: 10px;
    border: 1px solid rgba(139, 69, 19, 0.3);
}

.stat-group h4 {
    color: #d4af37;
    font-size: 0.9em;
    margin-bottom: 8px;
    text-align: center;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
    padding-bottom: 5px;
}

.stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 10px;
    background: rgba(139, 69, 19, 0.2);
    border-radius: 5px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.stat:hover {
    background: rgba(139, 69, 19, 0.4);
    transform: translateX(5px);
}

.stat-value {
    color: #ff6b35;
    font-weight: bold;
    font-size: 0.9em;
}

.pulse-button {
    animation: glow 2s infinite;
}

.upgrade-btn, .action-btn, .combat-btn, .close-btn {
    background: linear-gradient(135deg, #8b4513, #a0522d);
    color: #fff;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-family: inherit;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.upgrade-btn:hover, .action-btn:hover, .combat-btn:hover, .close-btn:hover {
    background: linear-gradient(135deg, #a0522d, #cd853f);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* 探索面板 */
.map-container {
    text-align: center;
}

.current-location {
    background: rgba(139, 69, 19, 0.3);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.current-location h4 {
    color: #d4af37;
    font-size: 1.4em;
    margin-bottom: 10px;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-btn.explore {
    background: linear-gradient(135deg, #228b22, #32cd32);
}

.action-btn.move {
    background: linear-gradient(135deg, #4169e1, #6495ed);
}

.action-btn.rest {
    background: linear-gradient(135deg, #ff6347, #ff7f50);
}

.action-btn.map {
    background: linear-gradient(135deg, #9370db, #ba55d3);
}

/* 物资清单 */
.inventory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.inventory-item {
    background: rgba(139, 69, 19, 0.3);
    border: 1px solid #654321;
    border-radius: 6px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.inventory-item:hover {
    background: rgba(139, 69, 19, 0.5);
    transform: scale(1.05);
}

.item-icon {
    font-size: 2em;
    margin-bottom: 5px;
}

.item-name {
    font-size: 0.9em;
    font-weight: bold;
}

.item-quantity {
    font-size: 0.8em;
    color: #d4af37;
}

.inventory-summary {
    text-align: center;
    padding-top: 10px;
    border-top: 1px solid #654321;
    color: #d4af37;
}

/* 游戏日志 */
.game-log {
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 10, 5, 0.9)),
        radial-gradient(circle at center, rgba(139, 69, 19, 0.15), transparent);
    border: 2px solid #654321;
    border-radius: 12px;
    padding: 15px;
    max-height: 140px;
    animation: slideIn 0.8s ease-out;
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.6),
        0 0 15px rgba(139, 69, 19, 0.4),
        0 0 30px rgba(255, 107, 53, 0.1);
    position: relative;
    overflow: hidden;
}

.game-log::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
        transparent,
        #ff6b35,
        #ff8c42,
        #ff6b35,
        transparent);
    animation: scanline 4s infinite;
}

@keyframes scanline {
    0% { transform: translateX(-100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
}

.game-log h4 {
    color: #ff6b35;
    margin-bottom: 12px;
    font-size: 1.1em;
    text-shadow: 0 0 8px rgba(255, 107, 53, 0.6);
    border-bottom: 1px solid rgba(255, 107, 53, 0.4);
    padding-bottom: 6px;
    text-align: center;
    letter-spacing: 1px;
}

.log-content {
    max-height: 100px;
    overflow-y: auto;
    padding-right: 8px;
    scrollbar-width: thin;
    scrollbar-color: #ff6b35 rgba(0, 0, 0, 0.4);
}

.log-content::-webkit-scrollbar {
    width: 6px;
}

.log-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 3px;
}

.log-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #ff6b35, #ff8c42);
    border-radius: 3px;
    box-shadow: 0 0 5px rgba(255, 107, 53, 0.5);
}

.log-entry {
    margin-bottom: 6px;
    padding: 8px 12px;
    background:
        linear-gradient(90deg,
            rgba(139, 69, 19, 0.25),
            rgba(139, 69, 19, 0.15),
            rgba(139, 69, 19, 0.08));
    border-radius: 6px;
    animation: logEntryAppear 0.6s ease-out;
    border-left: 4px solid #ff6b35;
    transition: all 0.3s ease;
    position: relative;
    font-size: 0.85em;
    line-height: 1.3;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

@keyframes logEntryAppear {
    0% {
        opacity: 0;
        transform: translateX(-30px) scale(0.9);
        filter: blur(2px);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
        filter: blur(0);
    }
}

.log-entry:hover {
    background:
        linear-gradient(90deg,
            rgba(139, 69, 19, 0.5),
            rgba(139, 69, 19, 0.35),
            rgba(139, 69, 19, 0.25));
    transform: translateX(10px);
    border-left-color: #ff8c42;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.5),
        0 0 15px rgba(255, 107, 53, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.log-entry::before {
    content: '▶';
    position: absolute;
    left: -2px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff6b35;
    font-size: 0.7em;
    opacity: 0;
    transition: all 0.3s ease;
    text-shadow: 0 0 5px rgba(255, 107, 53, 0.8);
}

.log-entry:hover::before {
    opacity: 1;
    left: 2px;
}

/* 状态栏动画效果 */
.status-bar-fill {
    position: relative;
    overflow: hidden;
}

.status-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 按钮增强效果 */
.action-btn, .upgrade-btn, .combat-btn {
    position: relative;
    overflow: hidden;
}

.action-btn::before, .upgrade-btn::before, .combat-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-btn:hover::before, .upgrade-btn:hover::before, .combat-btn:hover::before {
    left: 100%;
}

/* 升级界面 */
.upgrade-panel {
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 10, 5, 0.9)),
        radial-gradient(circle at 30% 70%, rgba(139, 69, 19, 0.2), transparent),
        radial-gradient(circle at 70% 30%, rgba(255, 107, 53, 0.1), transparent);
    border: 3px solid #8b4513;
    border-radius: 20px;
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
    box-shadow:
        0 0 30px rgba(139, 69, 19, 0.5),
        inset 0 0 50px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.upgrade-panel::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        #8b4513, #ff6b35, #8b4513, #ff6b35, #8b4513);
    background-size: 400% 400%;
    animation: borderGlow 4s ease-in-out infinite;
    border-radius: 20px;
    z-index: -1;
}

@keyframes borderGlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.upgrade-panel h2 {
    text-align: center;
    color: #ff6b35;
    font-size: 2.8em;
    margin-bottom: 30px;
    text-shadow:
        0 0 10px rgba(255, 107, 53, 0.8),
        0 0 20px rgba(255, 107, 53, 0.4);
    letter-spacing: 2px;
    position: relative;
}

.upgrade-panel h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #ff6b35, transparent);
}

.upgrade-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    max-height: 650px;
    overflow-y: auto;
    padding: 10px;
    scrollbar-width: thin;
    scrollbar-color: #ff6b35 rgba(0, 0, 0, 0.3);
}

.upgrade-categories::-webkit-scrollbar {
    width: 8px;
}

.upgrade-categories::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

.upgrade-categories::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #ff6b35, #ff8c42);
    border-radius: 4px;
    box-shadow: 0 0 5px rgba(255, 107, 53, 0.5);
}

.upgrade-category {
    background:
        linear-gradient(135deg,
            rgba(139, 69, 19, 0.3),
            rgba(139, 69, 19, 0.15));
    border: 2px solid #654321;
    border-radius: 15px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.upgrade-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 107, 53, 0.1),
        transparent);
    transition: left 0.5s ease;
}

.upgrade-category:hover {
    border-color: #ff6b35;
    transform: translateY(-5px);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(255, 107, 53, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.upgrade-category:hover::before {
    left: 100%;
}

.upgrade-category h3 {
    color: #d4af37;
    text-align: center;
    margin-bottom: 15px;
    font-size: 1.4em;
    text-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
    border-bottom: 2px solid rgba(212, 175, 55, 0.3);
    padding-bottom: 8px;
}

.upgrade-option {
    background:
        linear-gradient(135deg,
            rgba(139, 69, 19, 0.4),
            rgba(139, 69, 19, 0.2));
    border: 2px solid #654321;
    border-radius: 12px;
    padding: 18px;
    margin-bottom: 12px;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.upgrade-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    transition: left 0.6s ease;
}

.upgrade-option:hover::before {
    left: 100%;
}

.upgrade-option.affordable {
    border-color: #32cd32;
    background:
        linear-gradient(135deg,
            rgba(34, 139, 34, 0.3),
            rgba(34, 139, 34, 0.15));
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(50, 205, 50, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.upgrade-option.affordable:hover {
    transform: translateY(-3px);
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 25px rgba(50, 205, 50, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.upgrade-option.expensive {
    border-color: #dc143c;
    background:
        linear-gradient(135deg,
            rgba(139, 0, 0, 0.3),
            rgba(139, 0, 0, 0.15));
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(220, 20, 60, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.upgrade-option h4 {
    color: #ff6b35;
    margin-bottom: 10px;
    font-size: 1.2em;
    text-shadow: 0 0 5px rgba(255, 107, 53, 0.5);
    border-bottom: 1px solid rgba(255, 107, 53, 0.3);
    padding-bottom: 5px;
}

.upgrade-option p {
    color: #e0d5c7;
    margin-bottom: 12px;
    font-size: 0.9em;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.upgrade-option .cost {
    color: #d4af37;
    font-size: 0.85em;
    margin-bottom: 12px;
    font-style: italic;
    background: rgba(0, 0, 0, 0.3);
    padding: 6px 10px;
    border-radius: 6px;
    border-left: 3px solid #d4af37;
    text-shadow: 0 0 3px rgba(212, 175, 55, 0.5);
}

.upgrade-option button {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #8b4513, #a0522d);
    color: #fff;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: inherit;
    font-weight: bold;
    font-size: 0.95em;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.upgrade-option button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent);
    transition: left 0.5s ease;
}

.upgrade-option button:hover:not(:disabled) {
    background: linear-gradient(135deg, #a0522d, #cd853f);
    transform: translateY(-2px);
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(205, 133, 63, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.upgrade-option button:hover:not(:disabled)::before {
    left: 100%;
}

.upgrade-option button:disabled {
    background: linear-gradient(135deg, #666, #555);
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
    box-shadow:
        0 1px 2px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 战斗界面 */
.combat-panel {
    background: rgba(139, 0, 0, 0.9);
    border: 3px solid #8b0000;
    border-radius: 15px;
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.combat-panel h2 {
    color: #ff4444;
    font-size: 2.5em;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.combat-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.enemy-info {
    flex: 1;
}

.enemy-image {
    font-size: 5em;
    margin-bottom: 15px;
}

.enemy-health-bar {
    width: 200px;
    height: 20px;
    background: #333;
    border: 1px solid #666;
    border-radius: 10px;
    overflow: hidden;
    margin: 0 auto;
}

.status-bar-fill.enemy-health {
    background: linear-gradient(90deg, #ff0000, #ff4444);
    width: 100%;
}

.combat-actions {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.combat-btn.attack {
    background: linear-gradient(135deg, #dc143c, #ff1493);
}

.combat-btn.defend {
    background: linear-gradient(135deg, #4682b4, #87ceeb);
}

.combat-btn.flee {
    background: linear-gradient(135deg, #daa520, #ffd700);
}

.combat-log {
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid #654321;
    border-radius: 8px;
    padding: 15px;
    max-height: 150px;
    overflow-y: auto;
    text-align: left;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    #game-container {
        max-width: 100%;
        padding: 10px;
    }

    .main-game-area {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 10px;
    }

    .vehicle-panel, .exploration-panel, .inventory-panel {
        padding: 10px;
        max-height: 60vh;
    }
}

@media (max-width: 1200px) {
    .main-game-area {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .upgrade-categories {
        grid-template-columns: repeat(2, 1fr);
    }

    .status-bar {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .status-bar {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .status-item {
        justify-content: space-between;
    }

    .game-header h1 {
        font-size: 2em;
    }

    .combat-area {
        flex-direction: column;
        gap: 20px;
    }

    .upgrade-categories {
        grid-template-columns: 1fr;
    }

    .vehicle-panel, .exploration-panel, .inventory-panel {
        max-height: 50vh;
    }

    .game-log {
        max-height: 100px;
    }
}

/* 地图界面 */
.map-panel {
    background: rgba(0, 0, 0, 0.9);
    border: 3px solid #8b4513;
    border-radius: 15px;
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
    max-height: 90vh;
    overflow-y: auto;
}

.map-panel h2 {
    text-align: center;
    color: #ff6b35;
    font-size: 2.5em;
    margin-bottom: 20px;
}

.map-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(139, 69, 19, 0.2);
    border-radius: 8px;
}

.region-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
    color: #d4af37;
    font-weight: bold;
}

.region-controls {
    display: flex;
    gap: 10px;
}

.region-btn {
    background: linear-gradient(135deg, #8b4513, #a0522d);
    color: #fff;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-family: inherit;
    font-weight: bold;
    transition: all 0.3s ease;
}

.region-btn:hover {
    background: linear-gradient(135deg, #a0522d, #cd853f);
    transform: translateY(-2px);
}

.map-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(139, 69, 19, 0.1);
    border-radius: 10px;
}

.map-grid {
    display: grid;
    gap: 2px;
    background: #2c1810;
    padding: 10px;
    border-radius: 8px;
    border: 2px solid #654321;
}

.map-cell {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
}

.map-cell.current {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    border: 2px solid #fff;
    animation: pulse 2s infinite;
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.8);
    position: relative;
    z-index: 5;
}

.map-cell.explored {
    background: linear-gradient(135deg, #228b22, #32cd32);
    border: 1px solid #90ee90;
    box-shadow: 0 0 10px rgba(34, 139, 34, 0.5);
}

.map-cell.known {
    background: linear-gradient(135deg, #4169e1, #6495ed);
    border: 1px solid #87ceeb;
    color: #fff;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 0 8px rgba(65, 105, 225, 0.4);
}

.map-cell.known:hover {
    background: linear-gradient(135deg, #6495ed, #87ceeb);
    box-shadow: 0 0 15px rgba(65, 105, 225, 0.8);
}

.map-cell.unknown {
    background: #333;
    border: 1px solid #555;
    opacity: 0.6;
}

.map-cell:hover {
    transform: scale(1.1);
    z-index: 10;
    transition: all 0.2s ease;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 107, 53, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 107, 53, 0); }
}

.map-legend {
    background: rgba(139, 69, 19, 0.2);
    border: 1px solid #654321;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.map-legend h4 {
    color: #ff6b35;
    margin-bottom: 10px;
    text-align: center;
}

.legend-items {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9em;
}

.legend-icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-size: 1.1em;
}

.legend-icon.current {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    border: 1px solid #fff;
}

/* 回忆界面 */
.memory-panel {
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 10, 5, 0.9)),
        radial-gradient(circle at center, rgba(139, 69, 19, 0.2), transparent);
    border: 3px solid #8b4513;
    border-radius: 20px;
    padding: 30px;
    max-width: 1000px;
    margin: 0 auto;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow:
        0 0 30px rgba(139, 69, 19, 0.5),
        inset 0 0 50px rgba(0, 0, 0, 0.3);
}

.memory-panel h2 {
    text-align: center;
    color: #ff6b35;
    font-size: 2.5em;
    margin-bottom: 30px;
    text-shadow: 0 0 10px rgba(255, 107, 53, 0.8);
}

.memory-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(139, 69, 19, 0.2);
    border-radius: 10px;
}

.memory-stats .stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.memory-stats .stat-item span:first-child {
    display: block;
    color: #d4af37;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.memory-stats .stat-item span:last-child {
    color: #ff6b35;
    font-size: 1.5em;
    font-weight: bold;
}

.memory-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
}

.memory-item {
    background: rgba(139, 69, 19, 0.3);
    border: 2px solid #654321;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.memory-item:hover {
    border-color: #ff6b35;
    background: rgba(139, 69, 19, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.memory-item h4 {
    color: #ff6b35;
    margin-bottom: 8px;
    font-size: 1.2em;
}

.memory-item .memory-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    font-size: 0.9em;
}

.memory-item .memory-note {
    grid-column: 1 / -1;
    margin-top: 10px;
    padding: 8px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    font-style: italic;
    color: #d4af37;
}

/* 遭遇界面 */
.encounter-panel {
    background:
        linear-gradient(135deg, rgba(139, 0, 0, 0.95), rgba(80, 0, 0, 0.9)),
        radial-gradient(circle at center, rgba(255, 0, 0, 0.2), transparent);
    border: 3px solid #8b0000;
    border-radius: 20px;
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    box-shadow:
        0 0 30px rgba(139, 0, 0, 0.5),
        inset 0 0 50px rgba(0, 0, 0, 0.3);
}

.encounter-panel h2 {
    color: #ff4444;
    font-size: 2.5em;
    margin-bottom: 30px;
    text-shadow: 0 0 10px rgba(255, 68, 68, 0.8);
    animation: pulse 2s infinite;
}

.encounter-description {
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid #8b0000;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    font-size: 1.1em;
    line-height: 1.5;
    color: #e0d5c7;
}

.encounter-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.encounter-option {
    background: linear-gradient(135deg, #8b4513, #a0522d);
    color: #fff;
    border: 2px solid #654321;
    padding: 15px 20px;
    border-radius: 10px;
    cursor: pointer;
    font-family: inherit;
    font-size: 1em;
    transition: all 0.3s ease;
    text-align: left;
}

.encounter-option:hover {
    background: linear-gradient(135deg, #a0522d, #cd853f);
    border-color: #ff6b35;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.encounter-option .option-text {
    font-weight: bold;
    margin-bottom: 5px;
}

.encounter-option .option-details {
    font-size: 0.9em;
    color: #d4af37;
    font-style: italic;
}

/* 制作界面 */
.crafting-panel {
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 10, 5, 0.9)),
        radial-gradient(circle at center, rgba(139, 69, 19, 0.2), transparent);
    border: 3px solid #8b4513;
    border-radius: 20px;
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow:
        0 0 30px rgba(139, 69, 19, 0.5),
        inset 0 0 50px rgba(0, 0, 0, 0.3);
}

.crafting-panel h2 {
    text-align: center;
    color: #ff6b35;
    font-size: 2.5em;
    margin-bottom: 30px;
    text-shadow: 0 0 10px rgba(255, 107, 53, 0.8);
}

.crafting-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* 装备区域 */
.equipment-section {
    background: rgba(139, 69, 19, 0.2);
    border: 2px solid #654321;
    border-radius: 15px;
    padding: 20px;
}

.equipment-section h3 {
    color: #d4af37;
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.3em;
    text-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
}

.equipment-slots {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.equipment-slot {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.equipment-slot label {
    color: #ff6b35;
    font-weight: bold;
    min-width: 60px;
}

.equipped-item-slot {
    flex: 1;
    min-height: 40px;
    display: flex;
    align-items: center;
}

.equipped-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 10px;
    background: rgba(139, 69, 19, 0.4);
    border-radius: 6px;
    border: 1px solid #8b4513;
    width: 100%;
}

.equipped-item .item-icon {
    font-size: 1.2em;
}

.equipped-item .item-name {
    flex: 1;
    color: #e0d5c7;
    font-size: 0.9em;
}

.unequip-btn {
    background: linear-gradient(135deg, #8b0000, #a52a2a);
    color: #fff;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8em;
    transition: all 0.3s ease;
}

.unequip-btn:hover {
    background: linear-gradient(135deg, #a52a2a, #dc143c);
    transform: scale(1.05);
}

.empty-slot {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 10px;
    border: 2px dashed #444;
    border-radius: 6px;
    width: 100%;
}

.vehicle-parts-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 107, 53, 0.3);
}

.vehicle-parts-section h4 {
    color: #d4af37;
    margin-bottom: 10px;
}

.vehicle-parts-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.equipment-bonuses {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 107, 53, 0.3);
}

.equipment-bonuses h4 {
    color: #d4af37;
    margin-bottom: 10px;
}

.bonus-text {
    color: #e0d5c7;
    font-size: 0.9em;
    background: rgba(0, 0, 0, 0.3);
    padding: 8px;
    border-radius: 6px;
    border-left: 3px solid #d4af37;
}

/* 制作配方区域 */
.crafting-recipes {
    background: rgba(139, 69, 19, 0.2);
    border: 2px solid #654321;
    border-radius: 15px;
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
}

.crafting-recipes h3 {
    color: #d4af37;
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.3em;
    text-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
}

.crafting-categories {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.crafting-category h3 {
    color: #ff6b35;
    font-size: 1.2em;
    margin-bottom: 15px;
    border-bottom: 2px solid rgba(255, 107, 53, 0.3);
    padding-bottom: 5px;
}

.crafting-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.crafting-item {
    background: rgba(0, 0, 0, 0.4);
    border: 2px solid #654321;
    border-radius: 10px;
    padding: 15px;
    transition: all 0.3s ease;
    position: relative;
}

.crafting-item.craftable {
    border-color: #32cd32;
    box-shadow: 0 0 10px rgba(50, 205, 50, 0.3);
}

.crafting-item.not-craftable {
    border-color: #8b0000;
    opacity: 0.7;
}

.crafting-item:hover.craftable {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(50, 205, 50, 0.4);
}

.item-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.item-icon {
    font-size: 1.5em;
}

.item-header h4 {
    flex: 1;
    color: #ff6b35;
    margin: 0;
    font-size: 1.1em;
}

.item-level {
    background: rgba(212, 175, 55, 0.2);
    color: #d4af37;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8em;
    border: 1px solid #d4af37;
}

.item-description {
    color: #e0d5c7;
    font-size: 0.9em;
    margin-bottom: 10px;
    line-height: 1.3;
}

.item-materials {
    margin-bottom: 8px;
    font-size: 0.85em;
}

.item-materials strong {
    color: #d4af37;
}

.item-materials .available {
    color: #32cd32;
}

.item-materials .missing {
    color: #ff4444;
}

.item-time {
    color: #87ceeb;
    font-size: 0.8em;
    margin-bottom: 10px;
}

.item-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}

.item-stats .stat {
    background: rgba(255, 107, 53, 0.2);
    color: #ff6b35;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8em;
    border: 1px solid rgba(255, 107, 53, 0.5);
}

.craft-btn {
    width: 100%;
    padding: 10px;
    background: linear-gradient(135deg, #32cd32, #228b22);
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-family: inherit;
    font-weight: bold;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.craft-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #228b22, #006400);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.craft-btn:disabled {
    background: linear-gradient(135deg, #666, #555);
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
}

.legend-icon.explored {
    background: linear-gradient(135deg, #228b22, #32cd32);
    border: 1px solid #90ee90;
}

.legend-icon.known {
    background: linear-gradient(135deg, #4169e1, #6495ed);
    border: 1px solid #87ceeb;
    color: #fff;
    font-weight: bold;
}

.legend-icon.unknown {
    background: #333;
    border: 1px solid #555;
}
