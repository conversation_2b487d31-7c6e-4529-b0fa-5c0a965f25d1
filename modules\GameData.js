// 游戏数据模块
export class GameData {
    static getLocations() {
        return [
            {
                name: "废弃加油站",
                description: "一个破败的加油站，可能还有一些有用的物资...",
                loot: { fuel: [5, 15], food: [0, 3], scrap: [1, 4] },
                danger: 0.3,
                type: "common"
            },
            {
                name: "废弃超市",
                description: "曾经繁华的超市，现在只剩下空荡荡的货架...",
                loot: { food: [3, 8], water: [2, 6], medicine: [0, 2] },
                danger: 0.4,
                type: "common"
            },
            {
                name: "汽修厂",
                description: "满是工具和零件的汽修厂，对改装房车很有用...",
                loot: { scrap: [5, 12], fuel: [2, 8], tools: [0, 1] },
                danger: 0.5,
                type: "common"
            },
            {
                name: "居民区",
                description: "废弃的住宅区，家家户户都空无一人...",
                loot: { food: [1, 5], water: [1, 4], medicine: [0, 3], scrap: [0, 3] },
                danger: 0.6,
                type: "common"
            },
            {
                name: "军事基地",
                description: "被遗弃的军事基地，危险但可能有珍贵物资...",
                loot: { weapon_parts: [1, 3], medicine: [2, 5], fuel: [5, 15] },
                danger: 0.8,
                type: "common"
            },
            {
                name: "科研实验室",
                description: "高科技实验室，可能有先进的设备和化学物质...",
                loot: { electronics: [1, 3], chemicals: [2, 5], medicine: [1, 4], blueprints: [0, 1] },
                danger: 0.7,
                type: "rare"
            },
            {
                name: "核电站",
                description: "废弃的核电站，辐射很高但有珍贵的稀有金属...",
                loot: { rare_metals: [2, 6], electronics: [1, 3], fuel: [10, 20] },
                danger: 0.9,
                type: "rare",
                radiation: true
            },
            {
                name: "地下避难所",
                description: "一个隐蔽的地下避难所，里面可能有完好的物资...",
                loot: { food: [5, 15], water: [3, 10], medicine: [2, 6], batteries: [1, 3] },
                danger: 0.2,
                type: "rare"
            },
            {
                name: "废料场",
                description: "巨大的废料堆积场，虽然脏乱但有很多有用的材料...",
                loot: { scrap: [10, 25], electronics: [0, 2], rare_metals: [0, 2] },
                danger: 0.4,
                type: "common"
            },
            {
                name: "农场",
                description: "废弃的农场，可能还有一些种子和农产品...",
                loot: { food: [2, 8], seeds: [1, 4], water: [1, 5] },
                danger: 0.3,
                type: "common"
            },
            {
                name: "图书馆",
                description: "安静的图书馆，书籍可能包含有用的知识...",
                loot: { books: [1, 3], medicine: [0, 1], water: [0, 2] },
                danger: 0.2,
                type: "common"
            },
            {
                name: "化工厂",
                description: "危险的化工厂，有毒气体弥漫但化学物质丰富...",
                loot: { chemicals: [3, 8], fuel: [2, 10], filters: [1, 2] },
                danger: 0.8,
                type: "rare"
            },
            {
                name: "医院",
                description: "废弃的医院，医疗用品可能还有剩余...",
                loot: { medicine: [3, 10], electronics: [0, 2], chemicals: [1, 3] },
                danger: 0.6,
                type: "common"
            },
            {
                name: "秘密军火库",
                description: "隐藏的军火库，有大量武器零件和弹药...",
                loot: { weapon_parts: [3, 8], rare_metals: [1, 3], electronics: [1, 2] },
                danger: 0.9,
                type: "legendary"
            },
            {
                name: "太阳能发电站",
                description: "大型太阳能发电站，有很多电子设备和电池...",
                loot: { electronics: [2, 6], batteries: [2, 5], rare_metals: [1, 2] },
                danger: 0.5,
                type: "rare"
            }
        ];
    }

    static getEnemies() {
        return [
            // 普通敌人 (60%概率)
            {
                name: "游荡丧尸",
                health: 30,
                attack: 8,
                defense: 2,
                speed: 3,
                loot: { scrap: [0, 2] },
                type: "common",
                abilities: []
            },
            {
                name: "变异丧尸",
                health: 50,
                attack: 12,
                defense: 4,
                speed: 4,
                loot: { scrap: [1, 3], medicine: [0, 1] },
                type: "common",
                abilities: ["poison"]
            },
            {
                name: "丧尸群",
                health: 80,
                attack: 15,
                defense: 3,
                speed: 2,
                loot: { scrap: [2, 5], food: [0, 2] },
                type: "common",
                abilities: ["swarm"]
            },
            {
                name: "野生动物",
                health: 40,
                attack: 10,
                defense: 1,
                speed: 6,
                loot: { food: [2, 5] },
                type: "common",
                abilities: ["fast"]
            },
            {
                name: "暴徒团伙",
                health: 60,
                attack: 14,
                defense: 5,
                speed: 4,
                loot: { weapon_parts: [1, 2], fuel: [2, 5] },
                type: "common",
                abilities: ["armed"]
            },

            // 稀有敌人 (30%概率)
            {
                name: "辐射丧尸",
                health: 60,
                attack: 14,
                defense: 5,
                speed: 3,
                loot: { rare_metals: [0, 1], chemicals: [1, 2] },
                type: "rare",
                radiation: true,
                abilities: ["radiation"]
            },
            {
                name: "装甲丧尸",
                health: 100,
                attack: 18,
                defense: 8,
                speed: 2,
                loot: { scrap: [3, 6], weapon_parts: [0, 1] },
                type: "rare",
                abilities: ["armored"]
            },
            {
                name: "疯狂科学家",
                health: 40,
                attack: 10,
                defense: 3,
                speed: 4,
                loot: { electronics: [1, 3], chemicals: [2, 4], blueprints: [0, 1] },
                type: "rare",
                abilities: ["tech", "poison"]
            },
            {
                name: "机械守卫",
                health: 120,
                attack: 20,
                defense: 10,
                speed: 3,
                loot: { electronics: [2, 5], rare_metals: [1, 3] },
                type: "rare",
                abilities: ["mechanical", "repair"]
            },
            {
                name: "变异猎犬",
                health: 70,
                attack: 16,
                defense: 3,
                speed: 7,
                loot: { food: [1, 3], medicine: [0, 1] },
                type: "rare",
                abilities: ["fast", "pack"]
            },
            {
                name: "废土掠夺者",
                health: 90,
                attack: 20,
                defense: 6,
                speed: 5,
                loot: { weapon_parts: [2, 4], fuel: [3, 8], scrap: [2, 5] },
                type: "rare",
                abilities: ["armed", "tactical"]
            },

            // 传说敌人 (10%概率)
            {
                name: "变异巨兽",
                health: 200,
                attack: 25,
                defense: 6,
                speed: 2,
                loot: { rare_metals: [2, 4], medicine: [1, 3], food: [3, 8] },
                type: "legendary",
                abilities: ["giant", "regenerate"]
            },
            {
                name: "丧尸领主",
                health: 150,
                attack: 22,
                defense: 12,
                speed: 4,
                loot: { weapon_parts: [2, 4], electronics: [1, 2], blueprints: [1, 1] },
                type: "legendary",
                abilities: ["command", "armored"]
            },
            {
                name: "机械泰坦",
                health: 300,
                attack: 30,
                defense: 15,
                speed: 1,
                loot: { electronics: [5, 10], rare_metals: [3, 6], blueprints: [1, 2] },
                type: "legendary",
                abilities: ["mechanical", "laser", "shield"]
            },
            {
                name: "废土军阀",
                health: 180,
                attack: 28,
                defense: 10,
                speed: 5,
                loot: { weapon_parts: [4, 8], fuel: [5, 15], rare_metals: [1, 3] },
                type: "legendary",
                abilities: ["armed", "tactical", "leadership"]
            },
            {
                name: "变异女王",
                health: 250,
                attack: 20,
                defense: 8,
                speed: 3,
                loot: { chemicals: [3, 8], medicine: [2, 5], blueprints: [1, 2] },
                type: "legendary",
                abilities: ["spawn", "poison", "regenerate"]
            }
        ];
    }

    static getItemData() {
        return {
            icons: {
                food: '🥫',
                water: '💧',
                fuel: '⛽',
                scrap: '🔧',
                medicine: '💊',
                tools: '🔨',
                weapon_parts: '🔫',
                electronics: '📱',
                chemicals: '🧪',
                rare_metals: '💎',
                blueprints: '📋',
                batteries: '🔋',
                filters: '🌪️',
                seeds: '🌱',
                books: '📚',
                // 制作物品图标
                makeshift_knife: '🔪',
                pipe_gun: '🔫',
                crossbow: '🏹',
                plasma_rifle: '⚡',
                leather_vest: '🦺',
                metal_armor: '🛡️',
                tactical_vest: '🎽',
                power_armor: '🤖',
                lockpick_set: '🔓',
                scanner: '📡',
                hacking_device: '💻',
                multi_tool: '🔧',
                stim_pack: '💉',
                armor_patch: '🩹',
                energy_cell: '🔋',
                rad_away: '☢️',
                tactical_belt: '🎒',
                night_vision: '🥽',
                gas_mask: '😷',
                survival_kit: '🎯'
            },
            names: {
                food: '食物',
                water: '水',
                fuel: '燃料',
                scrap: '废料',
                medicine: '药品',
                tools: '工具',
                weapon_parts: '武器零件',
                electronics: '电子元件',
                chemicals: '化学物质',
                rare_metals: '稀有金属',
                blueprints: '设计图纸',
                batteries: '电池',
                filters: '过滤器',
                seeds: '种子',
                books: '书籍',
                // 制作物品名称
                makeshift_knife: '临时匕首',
                pipe_gun: '管制枪械',
                crossbow: '十字弩',
                plasma_rifle: '等离子步枪',
                leather_vest: '皮革背心',
                metal_armor: '金属护甲',
                tactical_vest: '战术背心',
                power_armor: '动力装甲',
                lockpick_set: '撬锁工具',
                scanner: '环境扫描仪',
                hacking_device: '黑客设备',
                multi_tool: '多功能工具',
                stim_pack: '兴奋剂',
                armor_patch: '护甲修补包',
                energy_cell: '能量电池',
                rad_away: '辐射清除剂',
                tactical_belt: '战术腰带',
                night_vision: '夜视镜',
                gas_mask: '防毒面具',
                survival_kit: '生存套装'
            },
            weights: {
                food: 1,
                water: 2,
                fuel: 3,
                scrap: 2,
                medicine: 0.5,
                tools: 5,
                weapon_parts: 3,
                electronics: 1,
                chemicals: 2,
                rare_metals: 4,
                blueprints: 0.1,
                batteries: 2,
                filters: 1,
                seeds: 0.5,
                books: 1
            }
        };
    }

    static getInitialGameState() {
        return {
            player: {
                health: 100,
                maxHealth: 100,
                hunger: 100,
                maxHunger: 100,
                fuel: 50,
                maxFuel: 100,
                radiation: 0,
                maxRadiation: 100,
                morale: 100,
                maxMorale: 100,
                speed: 5, // 人物速度，影响战斗和逃跑
                experience: 0,
                level: 1
            },
            map: {
                currentRegion: 'street',
                currentLocation: { x: 1, y: 1 },
                exploredLocations: new Set(),
                knownLocations: new Set(),
                regionLevel: 0,
                regionNames: ['街道', '村庄', '区域', '县城', '城市', '省份', '国家']
            },
            vehicle: {
                armor: 1,
                storage: 50,
                maxStorage: 50,
                weapon: 0,
                engine: 1,
                communication: 0,
                medical: 0,
                survival: 0,
                special: 0,
                protection: 0,
                detection: 0,
                power: 0,
                speed: 5, // 房车速度，影响移动和追逐
                condition: 100 // 房车状况，影响性能
            },
            inventory: {
                food: 5,
                water: 3,
                fuel: 10,
                scrap: 2,
                medicine: 1,
                electronics: 0,
                chemicals: 0,
                rare_metals: 0,
                blueprints: 0,
                batteries: 0,
                filters: 0,
                seeds: 0,
                books: 0
            },
            location: {
                name: "废弃加油站",
                description: "一个破败的加油站，可能还有一些有用的物资...",
                explored: false,
                danger: 0.3
            },
            day: 1,
            hour: 8, // 当前小时 (0-23)
            minute: 0, // 当前分钟 (0-59)
            inCombat: false,
            currentEnemy: null,
            // 回忆系统
            memories: {
                visitedLocations: new Map(), // 存储访问过的地点信息
                locationNotes: new Map(), // 地点备注
                resourceHistory: new Map(), // 资源发现历史
                dangerAssessment: new Map() // 危险评估
            },
            // 负重系统
            overweight: false,
            weightPenalty: 0, // 超重惩罚百分比
            // 遭遇系统
            lastEncounterTime: 0,
            encounterCooldown: 0,
            // 装备系统
            equipment: {
                armor: null,
                weapon: null,
                tool: null,
                accessory: null
            },
            // 制作系统
            craftingLevel: 1
        };
    }
}
