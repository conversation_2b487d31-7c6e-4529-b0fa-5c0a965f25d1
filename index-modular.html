<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>末日房车 - 废土生存 (模块化版本)</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="game-container">
        <!-- 游戏标题 -->
        <header class="game-header">
            <h1>末日房车</h1>
            <div class="subtitle">废土生存之旅 (模块化版本)</div>
            <div class="time-display">
                <div class="day-counter">第 <span id="day-counter">1</span> 天</div>
                <div class="time-counter">
                    <span id="time-display">08:00</span>
                    <span id="time-period">上午</span>
                </div>
                <div class="weather-display">
                    <span id="weather-condition">晴朗</span>
                </div>
            </div>
            <div class="header-controls">
                <button class="memory-btn" onclick="openMemoryPanel()">📖 回忆</button>
                <button class="crafting-btn" onclick="openCraftingPanel()">🔨 制作</button>
                <button class="sound-toggle" onclick="toggleSound()" id="sound-toggle">🔊 音效开启</button>
            </div>
        </header>

        <!-- 主游戏界面 -->
        <div id="game-screen" class="screen active">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-item">
                    <span class="status-label">生命值:</span>
                    <div class="status-bar-container">
                        <div class="status-bar-fill health" id="health-bar"></div>
                    </div>
                    <span id="health-text">100/100</span>
                </div>
                <div class="status-item">
                    <span class="status-label">饥饿度:</span>
                    <div class="status-bar-container">
                        <div class="status-bar-fill hunger" id="hunger-bar"></div>
                    </div>
                    <span id="hunger-text">100/100</span>
                </div>
                <div class="status-item">
                    <span class="status-label">燃料:</span>
                    <div class="status-bar-container">
                        <div class="status-bar-fill fuel" id="fuel-bar"></div>
                    </div>
                    <span id="fuel-text">50/100</span>
                </div>
                <div class="status-item">
                    <span class="status-label">辐射:</span>
                    <div class="status-bar-container">
                        <div class="status-bar-fill radiation" id="radiation-bar"></div>
                    </div>
                    <span id="radiation-text">0/100</span>
                </div>
                <div class="status-item">
                    <span class="status-label">士气:</span>
                    <div class="status-bar-container">
                        <div class="status-bar-fill morale" id="morale-bar"></div>
                    </div>
                    <span id="morale-text">100/100</span>
                </div>
            </div>

            <!-- 主要游戏区域 -->
            <div class="main-game-area">
                <!-- 左侧：房车状态 -->
                <div class="vehicle-panel">
                    <h3>🚐 房车状态</h3>
                    <div class="vehicle-display">
                        <div class="vehicle-image animated-vehicle">🚐</div>
                        <div class="vehicle-stats">
                            <div class="stat-group">
                                <h4>基础系统</h4>
                                <div class="stat">
                                    <span>🛡️ 装甲:</span>
                                    <span id="armor-level" class="stat-value">1级</span>
                                </div>
                                <div class="stat">
                                    <span>📦 储存:</span>
                                    <span id="storage-level" class="stat-value">50kg</span>
                                </div>
                                <div class="stat">
                                    <span>⚔️ 武器:</span>
                                    <span id="weapon-level" class="stat-value">无</span>
                                </div>
                                <div class="stat">
                                    <span>🔧 引擎:</span>
                                    <span id="engine-level" class="stat-value">标准</span>
                                </div>
                            </div>
                            <div class="stat-group">
                                <h4>高级系统</h4>
                                <div class="stat">
                                    <span>📡 通讯:</span>
                                    <span id="communication-level" class="stat-value">无</span>
                                </div>
                                <div class="stat">
                                    <span>🏥 医疗:</span>
                                    <span id="medical-level" class="stat-value">无</span>
                                </div>
                                <div class="stat">
                                    <span>🌿 生存:</span>
                                    <span id="survival-level" class="stat-value">无</span>
                                </div>
                                <div class="stat">
                                    <span>⭐ 特殊:</span>
                                    <span id="special-level" class="stat-value">无</span>
                                </div>
                            </div>
                            <div class="stat-group">
                                <h4>扩展系统</h4>
                                <div class="stat">
                                    <span>🛡️ 防护:</span>
                                    <span id="protection-level" class="stat-value">无</span>
                                </div>
                                <div class="stat">
                                    <span>🔍 探测:</span>
                                    <span id="detection-level" class="stat-value">无</span>
                                </div>
                                <div class="stat">
                                    <span>⚡ 动力:</span>
                                    <span id="power-level" class="stat-value">无</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="upgrade-btn pulse-button" onclick="openUpgradePanel()">🔧 升级改装</button>
                </div>

                <!-- 中间：地图和探索 -->
                <div class="exploration-panel">
                    <h3>废土探索</h3>
                    <div class="exploration-container">
                        <div class="current-location">
                            <h4 id="location-name">废弃加油站</h4>
                            <p id="location-description">一个破败的加油站，可能还有一些有用的物资...</p>
                        </div>
                        <div class="action-buttons">
                            <button class="action-btn explore" onclick="exploreLocation()">搜索物资</button>
                            <button class="action-btn move" onclick="moveToNextLocation()">随机探索</button>
                            <button class="action-btn map" onclick="openMapPanel()">打开地图</button>
                            <button class="action-btn rest" onclick="restAtLocation()">休息恢复</button>
                        </div>
                    </div>
                </div>

                <!-- 右侧：物资清单 -->
                <div class="inventory-panel">
                    <h3>物资清单</h3>
                    <div class="inventory-grid" id="inventory-grid">
                        <!-- 物资项目将通过JavaScript动态生成 -->
                    </div>
                    <div class="inventory-summary">
                        <div>总重量: <span id="total-weight">15/50</span> kg</div>
                        <div id="weight-warning" class="weight-warning" style="display: none;"></div>
                        <div id="vehicle-condition" class="vehicle-condition">房车状态良好</div>
                    </div>
                </div>
            </div>

            <!-- 底部：游戏日志 -->
            <div class="game-log">
                <h4>游戏日志</h4>
                <div class="log-content" id="game-log">
                    <div class="log-entry">游戏开始！你驾驶着房车在废土上开始了生存之旅...</div>
                </div>
            </div>
        </div>

        <!-- 地图界面 -->
        <div id="map-screen" class="screen">
            <div class="map-panel">
                <h2>废土地图</h2>
                <div class="map-info">
                    <div class="region-info">
                        <span>当前区域: <span id="current-region">青山县</span></span>
                        <span>坐标: <span id="current-coordinates">(2, 2)</span></span>
                    </div>
                    <div class="region-controls">
                        <button class="region-btn" onclick="changeRegionUp()">🔼 扩大范围</button>
                        <button class="region-btn" onclick="changeRegionDown()">🔽 缩小范围</button>
                    </div>
                </div>
                <div class="map-container">
                    <div class="map-grid" id="map-grid">
                        <!-- 地图网格将通过JavaScript生成 -->
                    </div>
                </div>
                <div class="map-legend">
                    <h4>图例</h4>
                    <div class="legend-items">
                        <div class="legend-item">
                            <span class="legend-icon current">🚐</span>
                            <span>当前位置</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-icon explored">🏘️</span>
                            <span>已探索</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-icon known">?</span>
                            <span>已知位置</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-icon unknown"></span>
                            <span>未知区域</span>
                        </div>
                    </div>
                </div>
                <button class="close-btn" onclick="closeMapPanel()">返回游戏</button>
            </div>
        </div>

        <!-- 升级界面 -->
        <div id="upgrade-screen" class="screen">
            <div class="upgrade-panel">
                <h2>房车升级</h2>
                <div class="upgrade-categories">
                    <div class="upgrade-category">
                        <h3>装甲防护</h3>
                        <div class="upgrade-options" id="armor-upgrades">
                            <!-- 升级选项将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h3>储存空间</h3>
                        <div class="upgrade-options" id="storage-upgrades">
                            <!-- 升级选项将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h3>武器系统</h3>
                        <div class="upgrade-options" id="weapon-upgrades">
                            <!-- 升级选项将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h3>引擎动力</h3>
                        <div class="upgrade-options" id="engine-upgrades">
                            <!-- 升级选项将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h3>通讯系统</h3>
                        <div class="upgrade-options" id="communication-upgrades">
                            <!-- 升级选项将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h3>医疗系统</h3>
                        <div class="upgrade-options" id="medical-upgrades">
                            <!-- 升级选项将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h3>生存系统</h3>
                        <div class="upgrade-options" id="survival-upgrades">
                            <!-- 升级选项将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h3>特殊装备</h3>
                        <div class="upgrade-options" id="special-upgrades">
                            <!-- 升级选项将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h3>防护系统</h3>
                        <div class="upgrade-options" id="protection-upgrades">
                            <!-- 升级选项将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h3>探测系统</h3>
                        <div class="upgrade-options" id="detection-upgrades">
                            <!-- 升级选项将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="upgrade-category">
                        <h3>动力系统</h3>
                        <div class="upgrade-options" id="power-upgrades">
                            <!-- 升级选项将通过JavaScript生成 -->
                        </div>
                    </div>
                </div>
                <button class="close-btn" onclick="closeUpgradePanel()">返回游戏</button>
            </div>
        </div>

        <!-- 战斗界面 -->
        <div id="combat-screen" class="screen">
            <div class="combat-panel">
                <h2>遭遇丧尸！</h2>
                <div class="combat-area">
                    <div class="enemy-info">
                        <div class="enemy-image">🧟</div>
                        <div class="enemy-stats">
                            <div>敌人: <span id="enemy-name">游荡丧尸</span></div>
                            <div class="enemy-health-bar">
                                <div class="status-bar-fill enemy-health" id="enemy-health-bar"></div>
                            </div>
                        </div>
                    </div>
                    <div class="combat-actions">
                        <button class="combat-btn attack" onclick="attackEnemy()">攻击</button>
                        <button class="combat-btn defend" onclick="defendAction()">防御</button>
                        <button class="combat-btn flee" onclick="fleeFromCombat()">逃跑</button>
                    </div>
                </div>
                <div class="combat-log" id="combat-log">
                    <!-- 战斗日志 -->
                </div>
            </div>
        </div>

        <!-- 回忆界面 -->
        <div id="memory-screen" class="screen">
            <div class="memory-panel">
                <h2>📖 废土回忆录</h2>
                <div class="memory-content">
                    <div class="memory-stats">
                        <div class="stat-item">
                            <span>已探索地点:</span>
                            <span id="explored-count">0</span>
                        </div>
                        <div class="stat-item">
                            <span>总访问次数:</span>
                            <span id="total-visits">0</span>
                        </div>
                        <div class="stat-item">
                            <span>发现资源:</span>
                            <span id="total-resources-found">0</span>
                        </div>
                    </div>
                    <div class="memory-list" id="memory-list">
                        <!-- 回忆列表将通过JavaScript生成 -->
                    </div>
                </div>
                <button class="close-btn" onclick="closeMemoryPanel()">返回游戏</button>
            </div>
        </div>

        <!-- 遭遇界面 -->
        <div id="encounter-screen" class="screen">
            <div class="encounter-panel">
                <h2 id="encounter-title">⚠️ 意外遭遇</h2>
                <div class="encounter-content">
                    <div class="encounter-description" id="encounter-description">
                        <!-- 遭遇描述 -->
                    </div>
                    <div class="encounter-options" id="encounter-options">
                        <!-- 选择选项将通过JavaScript生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 制作界面 -->
        <div id="crafting-screen" class="screen">
            <div class="crafting-panel">
                <h2>🔨 废土工坊</h2>
                <div class="crafting-content">
                    <div class="crafting-left">
                        <div class="equipment-section">
                            <h3>当前装备</h3>
                            <div class="equipment-slots">
                                <div class="equipment-slot">
                                    <label>护甲:</label>
                                    <div id="equipped-armor" class="equipped-item-slot">
                                        <div class="empty-slot">空</div>
                                    </div>
                                </div>
                                <div class="equipment-slot">
                                    <label>武器:</label>
                                    <div id="equipped-weapon" class="equipped-item-slot">
                                        <div class="empty-slot">空</div>
                                    </div>
                                </div>
                                <div class="equipment-slot">
                                    <label>工具:</label>
                                    <div id="equipped-tool" class="equipped-item-slot">
                                        <div class="empty-slot">空</div>
                                    </div>
                                </div>
                                <div class="equipment-slot">
                                    <label>辅助:</label>
                                    <div id="equipped-accessory" class="equipped-item-slot">
                                        <div class="empty-slot">空</div>
                                    </div>
                                </div>
                            </div>
                            <div class="equipment-bonuses">
                                <h4>装备加成</h4>
                                <div id="equipment-bonuses" class="bonus-text">无加成</div>
                            </div>
                        </div>
                    </div>
                    <div class="crafting-right">
                        <div class="crafting-recipes">
                            <h3>制作配方</h3>
                            <div id="crafting-categories" class="crafting-categories">
                                <!-- 制作配方将通过JavaScript生成 -->
                            </div>
                        </div>
                    </div>
                </div>
                <button class="close-btn" onclick="closeCraftingPanel()">返回游戏</button>
            </div>
        </div>
    </div>

    <!-- 使用模块化JavaScript -->
    <script type="module" src="game-modular.js"></script>

    <!-- 浏览器兼容性检查 -->
    <script nomodule>
        alert('您的浏览器不支持ES6模块，请使用现代浏览器（Chrome 61+, Firefox 60+, Safari 10.1+, Edge 16+）或使用非模块化版本');
    </script>
</body>
</html>
