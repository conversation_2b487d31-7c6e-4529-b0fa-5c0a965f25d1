// 回忆系统模块
export class MemorySystem {
    constructor(gameState, soundManager) {
        this.gameState = gameState;
        this.soundManager = soundManager;
    }

    // 记录访问过的地点
    recordVisit(locationKey, locationData, resourcesFound, dangerLevel) {
        const visitData = {
            name: locationData.name,
            type: locationData.type,
            visitCount: (this.gameState.memories.visitedLocations.get(locationKey)?.visitCount || 0) + 1,
            lastVisit: { day: this.gameState.day, hour: this.gameState.hour },
            resourcesFound: resourcesFound || [],
            averageDanger: dangerLevel,
            totalResourcesGained: (this.gameState.memories.visitedLocations.get(locationKey)?.totalResourcesGained || 0) + (resourcesFound?.length || 0),
            region: this.gameState.map.currentRegion,
            coordinates: { ...this.gameState.map.currentLocation }
        };

        this.gameState.memories.visitedLocations.set(locationKey, visitData);
        
        // 更新危险评估
        this.updateDangerAssessment(locationKey, dangerLevel);
        
        // 记录资源历史
        if (resourcesFound && resourcesFound.length > 0) {
            this.recordResourceHistory(locationKey, resourcesFound);
        }
    }

    // 更新危险评估
    updateDangerAssessment(locationKey, dangerLevel) {
        const current = this.gameState.memories.dangerAssessment.get(locationKey) || { total: 0, count: 0 };
        current.total += dangerLevel;
        current.count += 1;
        current.average = current.total / current.count;
        this.gameState.memories.dangerAssessment.set(locationKey, current);
    }

    // 记录资源历史
    recordResourceHistory(locationKey, resources) {
        const history = this.gameState.memories.resourceHistory.get(locationKey) || [];
        history.push({
            resources: [...resources],
            time: { day: this.gameState.day, hour: this.gameState.hour }
        });
        this.gameState.memories.resourceHistory.set(locationKey, history);
    }

    // 获取地点回忆信息
    getLocationMemory(locationKey) {
        return this.gameState.memories.visitedLocations.get(locationKey);
    }

    // 检查是否访问过某地点
    hasVisited(locationKey) {
        return this.gameState.memories.visitedLocations.has(locationKey);
    }

    // 计算返回已知地点的燃料折扣
    getReturnDiscount(locationKey) {
        const memory = this.getLocationMemory(locationKey);
        if (!memory) return 0;

        // 基础折扣：访问次数越多，折扣越大
        let discount = Math.min(0.5, memory.visitCount * 0.1); // 最大50%折扣
        
        // 最近访问加成
        const daysSinceLastVisit = this.gameState.day - memory.lastVisit.day;
        if (daysSinceLastVisit <= 3) {
            discount += 0.1; // 最近访问额外10%折扣
        }

        return Math.min(0.6, discount); // 最大60%折扣
    }

    // 获取地点的预期收益
    getExpectedRewards(locationKey) {
        const memory = this.getLocationMemory(locationKey);
        if (!memory) return null;

        const resourceHistory = this.gameState.memories.resourceHistory.get(locationKey) || [];
        const dangerAssessment = this.gameState.memories.dangerAssessment.get(locationKey);

        return {
            averageResources: memory.totalResourcesGained / memory.visitCount,
            dangerLevel: dangerAssessment?.average || 0,
            lastResources: resourceHistory[resourceHistory.length - 1]?.resources || [],
            reliability: Math.min(1, memory.visitCount / 5) // 访问5次后完全可靠
        };
    }

    // 生成返回地点的玩法内容
    generateReturnContent(locationKey) {
        const memory = this.getLocationMemory(locationKey);
        if (!memory) return null;

        const rewards = this.getExpectedRewards(locationKey);
        const content = [];

        // 1. 资源再生 - 时间越长，资源恢复越多
        const daysSinceLastVisit = this.gameState.day - memory.lastVisit.day;
        if (daysSinceLastVisit >= 2) {
            content.push({
                type: 'resource_regeneration',
                description: `由于${daysSinceLastVisit}天未访问，资源有所恢复`,
                multiplier: Math.min(2, 1 + daysSinceLastVisit * 0.2)
            });
        }

        // 2. 隐藏区域 - 熟悉度高可以发现隐藏内容
        if (memory.visitCount >= 3 && Math.random() < 0.3) {
            content.push({
                type: 'hidden_area',
                description: '凭借对此地的了解，你发现了一个隐藏区域',
                bonus: 'rare_resources'
            });
        }

        // 3. 安全路线 - 减少危险遭遇
        if (memory.visitCount >= 2) {
            content.push({
                type: 'safe_route',
                description: '你知道安全的路线，避免了危险',
                dangerReduction: 0.5
            });
        }

        // 4. 效率提升 - 快速搜索
        content.push({
            type: 'efficiency',
            description: '熟悉的环境让你搜索更加高效',
            timeReduction: 0.3,
            fuelSaving: this.getReturnDiscount(locationKey)
        });

        // 5. 情报网络 - 高访问次数可获得周边信息
        if (memory.visitCount >= 5) {
            content.push({
                type: 'intelligence',
                description: '你在此地建立了情报网络，获得了周边地区的信息',
                bonus: 'map_reveal'
            });
        }

        return content;
    }

    // 添加地点备注
    addLocationNote(locationKey, note) {
        this.gameState.memories.locationNotes.set(locationKey, {
            note: note,
            time: { day: this.gameState.day, hour: this.gameState.hour }
        });
    }

    // 获取地点备注
    getLocationNote(locationKey) {
        return this.gameState.memories.locationNotes.get(locationKey);
    }

    // 获取所有访问过的地点（用于UI显示）
    getAllVisitedLocations() {
        const locations = [];
        for (const [key, data] of this.gameState.memories.visitedLocations) {
            const rewards = this.getExpectedRewards(key);
            locations.push({
                key: key,
                data: data,
                rewards: rewards,
                discount: this.getReturnDiscount(key),
                note: this.getLocationNote(key)
            });
        }
        
        // 按最后访问时间排序
        return locations.sort((a, b) => {
            const timeA = a.data.lastVisit.day * 24 + a.data.lastVisit.hour;
            const timeB = b.data.lastVisit.day * 24 + b.data.lastVisit.hour;
            return timeB - timeA;
        });
    }

    // 快速返回到访问过的地点
    fastTravelTo(locationKey, mapSystem, addLogEntry, updateUI, updateMapDisplay) {
        const memory = this.getLocationMemory(locationKey);
        if (!memory) {
            addLogEntry("没有关于这个地点的记忆！");
            return false;
        }

        // 计算距离和燃料消耗
        const currentLoc = this.gameState.map.currentLocation;
        const targetLoc = memory.coordinates;
        const distance = Math.abs(currentLoc.x - targetLoc.x) + Math.abs(currentLoc.y - targetLoc.y);
        
        const baseFuelCost = distance * mapSystem.getCurrentMapData().fuelCost;
        const discount = this.getReturnDiscount(locationKey);
        const actualFuelCost = Math.ceil(baseFuelCost * (1 - discount));

        if (this.gameState.player.fuel < actualFuelCost) {
            addLogEntry(`燃料不足！需要${actualFuelCost}单位燃料（已享受${Math.floor(discount * 100)}%熟悉度折扣）`);
            return false;
        }

        // 执行快速移动
        this.soundManager.play('move');
        this.gameState.player.fuel -= actualFuelCost;
        this.gameState.map.currentLocation = { ...targetLoc };

        // 应用返回内容
        const returnContent = this.generateReturnContent(locationKey);
        if (returnContent) {
            returnContent.forEach(content => {
                addLogEntry(content.description);
            });
        }

        addLogEntry(`快速返回到${memory.name}，节省了${Math.floor(discount * 100)}%的燃料`);
        
        updateUI();
        updateMapDisplay();
        return true;
    }

    // 清理过期记忆（可选功能）
    cleanupOldMemories() {
        const maxAge = 30; // 30天后记忆开始模糊
        for (const [key, data] of this.gameState.memories.visitedLocations) {
            const age = this.gameState.day - data.lastVisit.day;
            if (age > maxAge) {
                // 记忆模糊，减少准确性
                data.reliability = Math.max(0.3, data.reliability - 0.1);
            }
        }
    }
}
