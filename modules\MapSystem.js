// 地图系统模块
export class MapSystem {
    constructor(gameState, soundManager) {
        this.gameState = gameState;
        this.soundManager = soundManager;
        this.worldData = this.generateCompleteWorld();
        this.currentViewLevel = 'country'; // 当前查看的地图层级
        this.navigationPath = []; // 导航路径，用于逐级选择
    }

    // 生成完整的世界地图数据
    generateCompleteWorld() {
        const world = {
            country: this.generateCountry(),
            provinces: {},
            cities: {},
            counties: {},
            districts: {},
            villages: {},
            streets: {},
            interestPoints: {}
        };

        // 生成所有层级的地图数据
        this.generateAllLevels(world);
        return world;
    }

    // 生成国家数据
    generateCountry() {
        return {
            id: 'country_001',
            name: '废土联邦',
            type: 'country',
            size: { width: 15, height: 15 },
            provinces: this.generateProvinceGrid(15, 15),
            discovered: false,
            explored: false
        };
    }

    // 生成省份网格
    generateProvinceGrid(width, height) {
        const provinces = {};
        const provinceCount = Math.floor(Math.random() * 8) + 8; // 8-15个省份

        for (let i = 0; i < provinceCount; i++) {
            let x, y, key;
            do {
                x = Math.floor(Math.random() * width);
                y = Math.floor(Math.random() * height);
                key = `${x},${y}`;
            } while (provinces[key]);

            provinces[key] = {
                id: `province_${i.toString().padStart(3, '0')}`,
                name: this.generateRandomName('province'),
                type: 'province',
                coordinates: { x, y },
                discovered: false,
                explored: false
            };
        }
        return provinces;
    }

    // 生成所有层级的地图数据
    generateAllLevels(world) {
        // 为每个省份生成城市
        Object.values(world.country.provinces).forEach(province => {
            world.provinces[province.id] = this.generateProvince(province);

            // 为每个城市生成县
            Object.values(world.provinces[province.id].cities).forEach(city => {
                world.cities[city.id] = this.generateCity(city);

                // 为每个县生成区
                Object.values(world.cities[city.id].counties).forEach(county => {
                    world.counties[county.id] = this.generateCounty(county);

                    // 为每个区生成村
                    Object.values(world.counties[county.id].districts).forEach(district => {
                        world.districts[district.id] = this.generateDistrict(district);

                        // 为每个村生成街道
                        Object.values(world.districts[district.id].villages).forEach(village => {
                            world.villages[village.id] = this.generateVillage(village);

                            // 为每个街道生成兴趣点
                            Object.values(world.villages[village.id].streets).forEach(street => {
                                world.streets[street.id] = this.generateStreet(street);
                            });
                        });
                    });
                });
            });
        });
    }

    // 生成省份详细数据
    generateProvince(provinceInfo) {
        const size = { width: 13, height: 13 };
        return {
            ...provinceInfo,
            size,
            cities: this.generateCityGrid(size.width, size.height, provinceInfo.id),
            fuelCost: 40
        };
    }

    // 生成城市网格
    generateCityGrid(width, height, parentId) {
        const cities = {};
        const cityCount = Math.floor(Math.random() * 6) + 5; // 5-10个城市

        for (let i = 0; i < cityCount; i++) {
            let x, y, key;
            do {
                x = Math.floor(Math.random() * width);
                y = Math.floor(Math.random() * height);
                key = `${x},${y}`;
            } while (cities[key]);

            cities[key] = {
                id: `${parentId}_city_${i.toString().padStart(3, '0')}`,
                name: this.generateRandomName('city'),
                type: 'city',
                coordinates: { x, y },
                parentId,
                discovered: false,
                explored: false
            };
        }
        return cities;
    }

    generateRandomName(type) {
        const prefixes = {
            street: ['废墟', '破败', '荒芜', '寂静', '阴暗', '残破', '幽暗', '凄凉'],
            village: ['枯木', '断桥', '荒草', '死水', '焦土', '废墟', '鬼影', '血月'],
            district: ['暗影', '钢铁', '废土', '辐射', '毒雾', '烈焰', '冰霜', '雷鸣'],
            county: ['黑石', '赤土', '灰烬', '血岩', '毒沼', '钢城', '废铁', '骨山'],
            city: ['末日', '废土', '钢铁', '暗黑', '血色', '毒雾', '雷暴', '冰封'],
            province: ['荒芜', '废土', '末日', '暗影', '血色', '毒雾', '钢铁', '死亡'],
            interestPoint: ['废弃', '破败', '荒废', '残破', '毁坏', '倒塌', '腐朽', '锈蚀']
        };

        const suffixes = {
            street: ['街', '巷', '路', '道', '弄'],
            village: ['村', '庄', '屯', '寨', '营'],
            district: ['区', '镇', '坊', '城', '地'],
            county: ['县', '郡', '域', '境', '土'],
            city: ['市', '城', '都', '镇', '堡'],
            province: ['省', '州', '域', '区', '地'],
            interestPoint: ['加油站', '超市', '医院', '居民楼', '学校', '工厂', '仓库', '商店', '银行', '图书馆']
        };

        const prefix = prefixes[type][Math.floor(Math.random() * prefixes[type].length)];
        const suffix = suffixes[type][Math.floor(Math.random() * suffixes[type].length)];

        return prefix + suffix;
    }

    // 生成城市详细数据
    generateCity(cityInfo) {
        const size = { width: 11, height: 11 };
        return {
            ...cityInfo,
            size,
            counties: this.generateCountyGrid(size.width, size.height, cityInfo.id),
            fuelCost: 25
        };
    }

    // 生成县网格
    generateCountyGrid(width, height, parentId) {
        const counties = {};
        const countyCount = Math.floor(Math.random() * 4) + 4; // 4-7个县

        for (let i = 0; i < countyCount; i++) {
            let x, y, key;
            do {
                x = Math.floor(Math.random() * width);
                y = Math.floor(Math.random() * height);
                key = `${x},${y}`;
            } while (counties[key]);

            counties[key] = {
                id: `${parentId}_county_${i.toString().padStart(3, '0')}`,
                name: this.generateRandomName('county'),
                type: 'county',
                coordinates: { x, y },
                parentId,
                discovered: false,
                explored: false
            };
        }
        return counties;
    }

    // 生成县详细数据
    generateCounty(countyInfo) {
        const size = { width: 9, height: 9 };
        return {
            ...countyInfo,
            size,
            districts: this.generateDistrictGrid(size.width, size.height, countyInfo.id),
            fuelCost: 15
        };
    }

    // 生成区网格
    generateDistrictGrid(width, height, parentId) {
        const districts = {};
        const districtCount = Math.floor(Math.random() * 3) + 3; // 3-5个区

        for (let i = 0; i < districtCount; i++) {
            let x, y, key;
            do {
                x = Math.floor(Math.random() * width);
                y = Math.floor(Math.random() * height);
                key = `${x},${y}`;
            } while (districts[key]);

            districts[key] = {
                id: `${parentId}_district_${i.toString().padStart(3, '0')}`,
                name: this.generateRandomName('district'),
                type: 'district',
                coordinates: { x, y },
                parentId,
                discovered: false,
                explored: false
            };
        }
        return districts;
    }

    // 生成区详细数据
    generateDistrict(districtInfo) {
        const size = { width: 7, height: 7 };
        return {
            ...districtInfo,
            size,
            villages: this.generateVillageGrid(size.width, size.height, districtInfo.id),
            fuelCost: 10
        };
    }

    // 生成村网格
    generateVillageGrid(width, height, parentId) {
        const villages = {};
        const villageCount = Math.floor(Math.random() * 3) + 2; // 2-4个村

        for (let i = 0; i < villageCount; i++) {
            let x, y, key;
            do {
                x = Math.floor(Math.random() * width);
                y = Math.floor(Math.random() * height);
                key = `${x},${y}`;
            } while (villages[key]);

            villages[key] = {
                id: `${parentId}_village_${i.toString().padStart(3, '0')}`,
                name: this.generateRandomName('village'),
                type: 'village',
                coordinates: { x, y },
                parentId,
                discovered: false,
                explored: false
            };
        }
        return villages;
    }

    // 生成村详细数据
    generateVillage(villageInfo) {
        const size = { width: 5, height: 5 };
        return {
            ...villageInfo,
            size,
            streets: this.generateStreetGrid(size.width, size.height, villageInfo.id),
            fuelCost: 5
        };
    }

    // 生成街道网格
    generateStreetGrid(width, height, parentId) {
        const streets = {};
        const streetCount = Math.floor(Math.random() * 2) + 2; // 2-3个街道

        for (let i = 0; i < streetCount; i++) {
            let x, y, key;
            do {
                x = Math.floor(Math.random() * width);
                y = Math.floor(Math.random() * height);
                key = `${x},${y}`;
            } while (streets[key]);

            streets[key] = {
                id: `${parentId}_street_${i.toString().padStart(3, '0')}`,
                name: this.generateRandomName('street'),
                type: 'street',
                coordinates: { x, y },
                parentId,
                discovered: false,
                explored: false
            };
        }
        return streets;
    }

    // 生成街道详细数据
    generateStreet(streetInfo) {
        const size = { width: 3, height: 3 };
        return {
            ...streetInfo,
            size,
            interestPoints: this.generateInterestPointGrid(size.width, size.height, streetInfo.id),
            fuelCost: 2
        };
    }

    // 生成兴趣点网格
    generateInterestPointGrid(width, height, parentId) {
        const interestPoints = {};
        const pointCount = Math.floor(Math.random() * 3) + 3; // 3-5个兴趣点

        for (let i = 0; i < pointCount; i++) {
            let x, y, key;
            do {
                x = Math.floor(Math.random() * width);
                y = Math.floor(Math.random() * height);
                key = `${x},${y}`;
            } while (interestPoints[key]);

            interestPoints[key] = {
                id: `${parentId}_poi_${i.toString().padStart(3, '0')}`,
                name: this.generateRandomName('interestPoint'),
                type: 'interestPoint',
                coordinates: { x, y },
                parentId,
                discovered: false,
                explored: false,
                danger: 0.1 + Math.random() * 0.4,
                resources: this.generateInterestPointResources()
            };
        }
        return interestPoints;
    }

    // 生成兴趣点资源
    generateInterestPointResources() {
        const resourceTypes = [
            'food', 'water', 'fuel', 'scrap', 'medicine',
            'electronics', 'chemicals', 'rare_metals', 'blueprints'
        ];
        const resources = {};

        // 随机生成1-3种资源
        const resourceCount = Math.floor(Math.random() * 3) + 1;
        for (let i = 0; i < resourceCount; i++) {
            const resourceType = resourceTypes[Math.floor(Math.random() * resourceTypes.length)];
            if (!resources[resourceType]) {
                resources[resourceType] = Math.floor(Math.random() * 5) + 1;
            }
        }

        return resources;
    }

    // 获取当前查看的地图数据
    getCurrentViewData() {
        switch (this.currentViewLevel) {
            case 'country':
                return this.worldData.country;
            case 'province':
                return this.worldData.provinces[this.navigationPath[0]];
            case 'city':
                return this.worldData.cities[this.navigationPath[1]];
            case 'county':
                return this.worldData.counties[this.navigationPath[2]];
            case 'district':
                return this.worldData.districts[this.navigationPath[3]];
            case 'village':
                return this.worldData.villages[this.navigationPath[4]];
            case 'street':
                return this.worldData.streets[this.navigationPath[5]];
            default:
                return this.worldData.country;
        }
    }

    // 获取当前实际位置的完整路径
    getCurrentLocationPath() {
        if (!this.gameState.map.currentLocation) {
            return null;
        }
        return this.gameState.map.currentLocation;
    }

    // 设置地图查看层级
    setViewLevel(level, path = []) {
        this.currentViewLevel = level;
        this.navigationPath = [...path];
    }

    // 点击地图位置进行导航
    navigateToLocation(x, y) {
        const currentData = this.getCurrentViewData();
        const locationKey = `${x},${y}`;

        switch (this.currentViewLevel) {
            case 'country':
                if (currentData.provinces[locationKey]) {
                    const province = currentData.provinces[locationKey];
                    this.setViewLevel('province', [province.id]);
                    return { success: true, message: `查看${province.name}` };
                }
                break;
            case 'province':
                if (currentData.cities[locationKey]) {
                    const city = currentData.cities[locationKey];
                    this.setViewLevel('city', [this.navigationPath[0], city.id]);
                    return { success: true, message: `查看${city.name}` };
                }
                break;
            case 'city':
                if (currentData.counties[locationKey]) {
                    const county = currentData.counties[locationKey];
                    this.setViewLevel('county', [this.navigationPath[0], this.navigationPath[1], county.id]);
                    return { success: true, message: `查看${county.name}` };
                }
                break;
            case 'county':
                if (currentData.districts[locationKey]) {
                    const district = currentData.districts[locationKey];
                    this.setViewLevel('district', [...this.navigationPath, district.id]);
                    return { success: true, message: `查看${district.name}` };
                }
                break;
            case 'district':
                if (currentData.villages[locationKey]) {
                    const village = currentData.villages[locationKey];
                    this.setViewLevel('village', [...this.navigationPath, village.id]);
                    return { success: true, message: `查看${village.name}` };
                }
                break;
            case 'village':
                if (currentData.streets[locationKey]) {
                    const street = currentData.streets[locationKey];
                    this.setViewLevel('street', [...this.navigationPath, street.id]);
                    return { success: true, message: `查看${street.name}` };
                }
                break;
            case 'street':
                if (currentData.interestPoints[locationKey]) {
                    const poi = currentData.interestPoints[locationKey];
                    return this.moveToInterestPoint(poi);
                }
                break;
        }

        return { success: false, message: '无法导航到该位置' };
    }

    // 移动到兴趣点（实际移动）
    moveToInterestPoint(poi) {
        // 计算移动成本
        const currentLocation = this.getCurrentLocationPath();
        let fuelCost = 10; // 基础移动成本

        if (currentLocation && currentLocation.interestPointId !== poi.id) {
            // 如果是在同一街道内移动，成本较低
            if (currentLocation.streetId === poi.parentId) {
                fuelCost = 2;
            } else {
                // 跨街道移动成本较高
                fuelCost = this.calculateMovementCost(currentLocation, poi);
            }
        }

        if (this.gameState.player.fuel < fuelCost) {
            return {
                success: false,
                message: `燃料不足！需要${fuelCost}单位燃料，但只有${this.gameState.player.fuel}单位。`
            };
        }

        // 执行移动
        this.soundManager.play('move');
        this.gameState.player.fuel = Math.max(0, this.gameState.player.fuel - fuelCost);

        // 更新当前位置
        this.gameState.map.currentLocation = {
            countryId: this.worldData.country.id,
            provinceId: this.navigationPath[0],
            cityId: this.navigationPath[1],
            countyId: this.navigationPath[2],
            districtId: this.navigationPath[3],
            villageId: this.navigationPath[4],
            streetId: this.navigationPath[5],
            interestPointId: poi.id,
            coordinates: poi.coordinates
        };

        // 标记兴趣点为已发现和已探索
        poi.discovered = true;
        poi.explored = true;

        // 更新游戏位置信息
        this.gameState.location = {
            name: poi.name,
            description: this.generateLocationDescription(poi),
            explored: false,
            danger: poi.danger,
            type: poi.type,
            radiation: Math.random() < 0.1, // 10%概率有辐射
            mapLocation: this.getFullLocationName()
        };

        return {
            success: true,
            message: `到达了${this.getFullLocationName()}的${poi.name}`
        };
    }

    // 计算移动成本
    calculateMovementCost(from, to) {
        // 基础成本根据层级差异计算
        let cost = 10;

        if (from.streetId !== to.parentId) {
            cost += 5; // 跨街道
        }
        if (from.villageId !== this.navigationPath[4]) {
            cost += 10; // 跨村
        }
        if (from.districtId !== this.navigationPath[3]) {
            cost += 20; // 跨区
        }
        if (from.countyId !== this.navigationPath[2]) {
            cost += 30; // 跨县
        }
        if (from.cityId !== this.navigationPath[1]) {
            cost += 50; // 跨市
        }
        if (from.provinceId !== this.navigationPath[0]) {
            cost += 80; // 跨省
        }

        return cost;
    }

    // 生成位置描述
    generateLocationDescription(poi) {
        const descriptions = {
            '废弃加油站': '一个破败的加油站，油泵早已停止工作，但可能还有一些有用的物资...',
            '废弃超市': '曾经繁华的超市现在空空如也，货架倒塌，但角落里可能还藏着一些食物...',
            '废弃医院': '医院的走廊里回荡着诡异的声音，医疗设备散落一地，但可能还有药品...',
            '废弃居民楼': '高楼大厦现在成了废墟，窗户破碎，但住户可能留下了一些有用的东西...',
            '废弃学校': '教室里的课桌椅东倒西歪，黑板上还残留着粉笔字迹...',
            '废弃工厂': '巨大的机器已经锈蚀，但工厂里可能还有一些金属材料和工具...',
            '废弃仓库': '空旷的仓库里堆满了各种杂物，需要仔细搜索才能找到有用的物品...',
            '废弃商店': '小商店的门窗都已破损，但店主可能在某个角落藏了一些货物...',
            '废弃银行': '银行的保险柜大多已被撬开，但可能还有一些贵重物品遗留...',
            '废弃图书馆': '书籍散落一地，大部分已经发霉，但可能还有一些有价值的资料...'
        };

        return descriptions[poi.name] || '一个神秘的废弃建筑，不知道里面会有什么...';
    }

    // 获取完整位置名称
    getFullLocationName() {
        if (!this.navigationPath.length) return '未知位置';

        const parts = [];
        if (this.navigationPath[0]) {
            const province = this.worldData.provinces[this.navigationPath[0]];
            parts.push(province?.name || '未知省');
        }
        if (this.navigationPath[1]) {
            const city = this.worldData.cities[this.navigationPath[1]];
            parts.push(city?.name || '未知市');
        }
        if (this.navigationPath[2]) {
            const county = this.worldData.counties[this.navigationPath[2]];
            parts.push(county?.name || '未知县');
        }
        if (this.navigationPath[3]) {
            const district = this.worldData.districts[this.navigationPath[3]];
            parts.push(district?.name || '未知区');
        }
        if (this.navigationPath[4]) {
            const village = this.worldData.villages[this.navigationPath[4]];
            parts.push(village?.name || '未知村');
        }
        if (this.navigationPath[5]) {
            const street = this.worldData.streets[this.navigationPath[5]];
            parts.push(street?.name || '未知街道');
        }

        return parts.join(' ');
    }

    // 地图层级导航
    navigateUp() {
        const levels = ['street', 'village', 'district', 'county', 'city', 'province', 'country'];
        const currentIndex = levels.indexOf(this.currentViewLevel);

        if (currentIndex < levels.length - 1) {
            const newLevel = levels[currentIndex + 1];
            const newPath = this.navigationPath.slice(0, currentIndex);
            this.setViewLevel(newLevel, newPath);
            return { success: true, message: `切换到${newLevel}级视图` };
        }

        return { success: false, message: '已经是最高级别视图' };
    }

    navigateDown() {
        // 只有在选择了具体位置后才能向下导航
        return { success: false, message: '请先选择一个位置' };
    }

    // 获取位置图标
    getLocationIcon(type, discovered = false) {
        if (!discovered) {
            return '❓'; // 未发现的位置显示问号
        }

        const icons = {
            province: '🏛️',
            city: '�',
            county: '🏘️',
            district: '�',
            village: '�',
            street: '🛣️',
            interestPoint: '📍'
        };
        return icons[type] || '📍';
    }

    // 初始化地图系统
    initializeMap(locations) {
        // 设置初始位置为某个省的某个城市的某个县的某个区的某个村的某个街道的某个兴趣点
        const firstProvince = Object.values(this.worldData.country.provinces)[0];
        const firstCity = Object.values(this.worldData.provinces[firstProvince.id].cities)[0];
        const firstCounty = Object.values(this.worldData.cities[firstCity.id].counties)[0];
        const firstDistrict = Object.values(this.worldData.counties[firstCounty.id].districts)[0];
        const firstVillage = Object.values(this.worldData.districts[firstDistrict.id].villages)[0];
        const firstStreet = Object.values(this.worldData.villages[firstVillage.id].streets)[0];
        const firstPOI = Object.values(this.worldData.streets[firstStreet.id].interestPoints)[0];

        // 标记初始位置为已发现
        firstProvince.discovered = true;
        firstCity.discovered = true;
        firstCounty.discovered = true;
        firstDistrict.discovered = true;
        firstVillage.discovered = true;
        firstStreet.discovered = true;
        firstPOI.discovered = true;

        // 设置当前位置
        this.gameState.map.currentLocation = {
            countryId: this.worldData.country.id,
            provinceId: firstProvince.id,
            cityId: firstCity.id,
            countyId: firstCounty.id,
            districtId: firstDistrict.id,
            villageId: firstVillage.id,
            streetId: firstStreet.id,
            interestPointId: firstPOI.id,
            coordinates: firstPOI.coordinates
        };

        // 设置导航路径到当前街道
        this.setViewLevel('street', [
            firstProvince.id,
            firstCity.id,
            firstCounty.id,
            firstDistrict.id,
            firstVillage.id,
            firstStreet.id
        ]);

        // 设置游戏位置信息
        this.gameState.location = {
            name: firstPOI.name,
            description: this.generateLocationDescription(firstPOI),
            explored: false,
            danger: firstPOI.danger,
            type: firstPOI.type,
            radiation: false,
            mapLocation: this.getFullLocationName()
        };
    }

    // 兼容旧接口的方法
    moveToLocation(x, y, locations, addLogEntry, updateUI, updateMapDisplay) {
        const result = this.navigateToLocation(x, y);
        addLogEntry(result.message);
        updateUI();
        updateMapDisplay();
        return result.success;
    }

    changeRegion(direction, addLogEntry, updateUI, updateMapDisplay) {
        let result;
        if (direction === 'up') {
            result = this.navigateUp();
        } else {
            result = this.navigateDown();
        }
        addLogEntry(result.message);
        updateUI();
        updateMapDisplay();
    }

    // 获取当前地图数据（兼容旧接口）
    getCurrentMapData() {
        const currentData = this.getCurrentViewData();
        return {
            name: currentData.name || '未知区域',
            size: currentData.size?.width || 15,
            type: currentData.type || 'unknown'
        };
    }

    // 兼容方法
    getLocationKey(x, y) {
        return `${x},${y}`;
    }

    isLocationKnown(x, y) {
        const currentData = this.getCurrentViewData();
        const locationKey = this.getLocationKey(x, y);

        switch (this.currentViewLevel) {
            case 'country':
                return currentData.provinces[locationKey]?.discovered || false;
            case 'province':
                return currentData.cities[locationKey]?.discovered || false;
            case 'city':
                return currentData.counties[locationKey]?.discovered || false;
            case 'county':
                return currentData.districts[locationKey]?.discovered || false;
            case 'district':
                return currentData.villages[locationKey]?.discovered || false;
            case 'village':
                return currentData.streets[locationKey]?.discovered || false;
            case 'street':
                return currentData.interestPoints[locationKey]?.discovered || false;
            default:
                return false;
        }
    }

    isLocationExplored(x, y) {
        const currentData = this.getCurrentViewData();
        const locationKey = this.getLocationKey(x, y);

        switch (this.currentViewLevel) {
            case 'street':
                return currentData.interestPoints[locationKey]?.explored || false;
            default:
                return this.isLocationKnown(x, y);
        }
    }
}
