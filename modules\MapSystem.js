// 地图系统模块
export class MapSystem {
    constructor(gameState, soundManager) {
        this.gameState = gameState;
        this.soundManager = soundManager;
        this.mapData = this.initializeMapData();
    }

    initializeMapData() {
        return {
            // 街道级 - 最小单位
            street: {
                name: this.generateRandomName('street'),
                size: 3,
                fuelCost: 2,
                parentRegion: 'village',
                parentCoords: { x: 1, y: 1 },
                locations: this.generateStreetMap(),
                type: 'street'
            },
            // 村级
            village: {
                name: this.generateRandomName('village'),
                size: 5,
                fuelCost: 5,
                parentRegion: 'district',
                parentCoords: { x: 2, y: 2 },
                streets: this.generateVillageStreets(),
                locations: this.generateVillageMap(),
                type: 'village'
            },
            // 区级
            district: {
                name: this.generateRandomName('district'),
                size: 7,
                fuelCost: 10,
                parentRegion: 'county',
                parentCoords: { x: 3, y: 3 },
                villages: this.generateDistrictVillages(),
                locations: this.generateDistrictMap(),
                type: 'district'
            },
            // 县级
            county: {
                name: this.generateRandomName('county'),
                size: 9,
                fuelCost: 15,
                parentRegion: 'city',
                parentCoords: { x: 4, y: 4 },
                districts: this.generateCountyDistricts(),
                locations: this.generateCountyMap(),
                type: 'county'
            },
            // 市级
            city: {
                name: this.generateRandomName('city'),
                size: 11,
                fuelCost: 25,
                parentRegion: 'province',
                parentCoords: { x: 5, y: 5 },
                counties: this.generateCityCounties(),
                locations: this.generateCityMap(),
                type: 'city'
            },
            // 省级
            province: {
                name: this.generateRandomName('province'),
                size: 13,
                fuelCost: 40,
                parentRegion: 'country',
                parentCoords: { x: 6, y: 6 },
                cities: this.generateProvinceCities(),
                locations: this.generateProvinceMap(),
                type: 'province'
            },
            // 国级
            country: {
                name: '废土联邦',
                size: 15,
                fuelCost: 60,
                parentRegion: null,
                parentCoords: null,
                provinces: this.generateCountryProvinces(),
                locations: this.generateCountryMap(),
                type: 'country'
            }
        };
    }

    generateRandomName(type) {
        const prefixes = {
            street: ['废墟', '破败', '荒芜', '寂静', '阴暗', '残破', '幽暗', '凄凉'],
            village: ['枯木', '断桥', '荒草', '死水', '焦土', '废墟', '鬼影', '血月'],
            district: ['暗影', '钢铁', '废土', '辐射', '毒雾', '烈焰', '冰霜', '雷鸣'],
            county: ['黑石', '赤土', '灰烬', '血岩', '毒沼', '钢城', '废铁', '骨山'],
            city: ['末日', '废土', '钢铁', '暗黑', '血色', '毒雾', '雷暴', '冰封'],
            province: ['荒芜', '废土', '末日', '暗影', '血色', '毒雾', '钢铁', '死亡']
        };

        const suffixes = {
            street: ['街', '巷', '路', '道', '弄'],
            village: ['村', '庄', '屯', '寨', '营'],
            district: ['区', '镇', '坊', '城', '地'],
            county: ['县', '郡', '域', '境', '土'],
            city: ['市', '城', '都', '镇', '堡'],
            province: ['省', '州', '域', '区', '地']
        };

        const prefix = prefixes[type][Math.floor(Math.random() * prefixes[type].length)];
        const suffix = suffixes[type][Math.floor(Math.random() * suffixes[type].length)];

        return prefix + suffix;
    }

    generateStreetMap() {
        return {
            '1,1': { type: 'ruins', name: '废墟建筑', locations: ['废弃加油站', '居民区'] },
            '0,1': { type: 'alley', name: '黑暗小巷', locations: ['废料场'] },
            '2,1': { type: 'square', name: '破败广场', locations: ['废弃超市'] },
            '1,0': { type: 'road', name: '断裂公路', locations: ['废弃加油站'] },
            '1,2': { type: 'park', name: '荒废公园', locations: ['农场'] }
        };
    }

    generateVillageStreets() {
        const streets = {};
        for (let i = 0; i < 5; i++) {
            const x = Math.floor(Math.random() * 5);
            const y = Math.floor(Math.random() * 5);
            const key = `${x},${y}`;
            if (!streets[key]) {
                streets[key] = {
                    name: this.generateRandomName('street'),
                    type: 'street',
                    danger: 0.1 + Math.random() * 0.3
                };
            }
        }
        return streets;
    }

    generateVillageMap() {
        return {
            '2,2': { type: 'center', name: '村中心', locations: ['废弃超市', '居民区'] },
            '1,1': { type: 'farm', name: '废弃农田', locations: ['农场'] },
            '3,1': { type: 'house', name: '废弃民居', locations: ['居民区'] },
            '1,3': { type: 'well', name: '枯井', locations: ['地下避难所'] },
            '3,3': { type: 'barn', name: '破谷仓', locations: ['废料场'] }
        };
    }

    generateDistrictVillages() {
        const villages = {};
        for (let i = 0; i < 7; i++) {
            const x = Math.floor(Math.random() * 7);
            const y = Math.floor(Math.random() * 7);
            const key = `${x},${y}`;
            if (!villages[key]) {
                villages[key] = {
                    name: this.generateRandomName('village'),
                    type: 'village',
                    danger: 0.2 + Math.random() * 0.4,
                    resources: ['basic', 'agriculture', 'ruins'][Math.floor(Math.random() * 3)]
                };
            }
        }
        return villages;
    }

    generateDistrictMap() {
        return {
            '3,3': { type: 'center', name: '区政府', locations: ['军事基地', '医院'] },
            '1,1': { type: 'industrial', name: '工业区', locations: ['汽修厂', '化工厂'] },
            '5,1': { type: 'residential', name: '住宅区', locations: ['居民区', '废弃超市'] },
            '1,5': { type: 'commercial', name: '商业区', locations: ['废弃超市', '图书馆'] },
            '5,5': { type: 'transport', name: '交通枢纽', locations: ['废弃加油站'] }
        };
    }

    generateCountyDistricts() {
        const districts = {};
        for (let i = 0; i < 9; i++) {
            const x = Math.floor(Math.random() * 9);
            const y = Math.floor(Math.random() * 9);
            const key = `${x},${y}`;
            if (!districts[key]) {
                districts[key] = {
                    name: this.generateRandomName('district'),
                    type: 'district',
                    danger: 0.3 + Math.random() * 0.5,
                    resources: ['basic', 'industrial', 'commercial', 'residential'][Math.floor(Math.random() * 4)]
                };
            }
        }
        return districts;
    }

    generateCityCounties() {
        return {
            '2,2': { name: '青山县', type: 'county', danger: 0.3, resources: 'basic' },
            '1,1': { name: '柳溪县', type: 'county', danger: 0.2, resources: 'agriculture' },
            '3,1': { name: '石桥县', type: 'county', danger: 0.4, resources: 'industrial' },
            '1,3': { name: '梅花县', type: 'county', danger: 0.3, resources: 'medical' },
            '3,3': { name: '竹林县', type: 'county', danger: 0.2, resources: 'agriculture' },
            '0,2': { name: '西郊县', type: 'county', danger: 0.5, resources: 'transport' },
            '4,2': { name: '东郊县', type: 'county', danger: 0.5, resources: 'transport' },
            '2,0': { name: '北山县', type: 'county', danger: 0.6, resources: 'mining' },
            '2,4': { name: '南湖县', type: 'county', danger: 0.3, resources: 'tourism' }
        };
    }

    generateProvinceCities() {
        return {
            '4,4': { name: '江南市', type: 'city', danger: 0.4, resources: 'comprehensive', population: 'large' },
            '2,2': { name: '青山市', type: 'city', danger: 0.3, resources: 'agriculture', population: 'medium' },
            '6,2': { name: '东海市', type: 'city', danger: 0.5, resources: 'coastal', population: 'large' },
            '2,6': { name: '南山市', type: 'city', danger: 0.4, resources: 'mining', population: 'medium' },
            '1,1': { name: '西北市', type: 'city', danger: 0.6, resources: 'military', population: 'small' },
            '7,1': { name: '东北市', type: 'city', danger: 0.5, resources: 'industrial', population: 'medium' },
            '1,7': { name: '西南市', type: 'city', danger: 0.7, resources: 'desert', population: 'small' },
            '7,7': { name: '东南市', type: 'city', danger: 0.4, resources: 'forest', population: 'medium' },
            '4,0': { name: '北部市', type: 'city', danger: 0.6, resources: 'border', population: 'small' },
            '4,8': { name: '南部市', type: 'city', danger: 0.5, resources: 'border', population: 'small' }
        };
    }

    generateCountryProvinces() {
        return {
            '5,5': { name: '华东省', type: 'province', danger: 0.4, resources: 'comprehensive', development: 'high' },
            '3,3': { name: '华南省', type: 'province', danger: 0.5, resources: 'coastal', development: 'high' },
            '7,3': { name: '华北省', type: 'province', danger: 0.6, resources: 'industrial', development: 'medium' },
            '3,7': { name: '西南省', type: 'province', danger: 0.7, resources: 'mountain', development: 'low' },
            '7,7': { name: '东北省', type: 'province', danger: 0.6, resources: 'forest', development: 'medium' },
            '1,1': { name: '西北省', type: 'province', danger: 0.8, resources: 'desert', development: 'low' },
            '9,1': { name: '远东省', type: 'province', danger: 0.7, resources: 'border', development: 'low' },
            '1,9': { name: '西南边省', type: 'province', danger: 0.8, resources: 'border', development: 'low' },
            '9,9': { name: '东南省', type: 'province', danger: 0.5, resources: 'coastal', development: 'medium' },
            '5,1': { name: '北方省', type: 'province', danger: 0.7, resources: 'cold', development: 'medium' },
            '5,9': { name: '南方省', type: 'province', danger: 0.4, resources: 'tropical', development: 'high' }
        };
    }

    generateCountyMap() {
        return {
            '2,2': { type: 'town', name: '青山镇', locations: ['废弃加油站', '废弃超市', '居民区'] },
            '1,1': { type: 'village', name: '柳树村', locations: ['农场', '图书馆'] },
            '3,1': { type: 'village', name: '石桥村', locations: ['汽修厂', '废料场'] },
            '1,3': { type: 'village', name: '梅花村', locations: ['医院', '居民区'] },
            '3,3': { type: 'village', name: '竹林村', locations: ['农场', '废弃超市'] },
            '0,2': { type: 'highway', name: '西出口', locations: ['废弃加油站'] },
            '4,2': { type: 'highway', name: '东出口', locations: ['废弃加油站'] },
            '2,0': { type: 'highway', name: '北出口', locations: ['废弃加油站'] },
            '2,4': { type: 'highway', name: '南出口', locations: ['废弃加油站'] },
            '1,2': { type: 'forest', name: '青山森林', locations: ['废料场'] },
            '3,2': { type: 'forest', name: '竹海', locations: ['废料场'] },
            '2,1': { type: 'mountain', name: '青山', locations: ['科研实验室'] },
            '2,3': { type: 'lake', name: '青山湖', locations: ['地下避难所'] }
        };
    }

    generateCityMap() {
        return {
            '3,3': { type: 'downtown', name: '市中心', locations: ['医院', '图书馆', '废弃超市'] },
            '2,2': { type: 'district', name: '青山区', locations: ['居民区', '汽修厂'] },
            '4,2': { type: 'district', name: '江南区', locations: ['废弃超市', '医院'] },
            '2,4': { type: 'district', name: '新城区', locations: ['科研实验室', '太阳能发电站'] },
            '1,1': { type: 'industrial', name: '工业园', locations: ['化工厂', '废料场'] },
            '5,1': { type: 'industrial', name: '开发区', locations: ['汽修厂', '废料场'] },
            '1,5': { type: 'suburb', name: '南郊', locations: ['农场', '居民区'] },
            '5,5': { type: 'suburb', name: '东郊', locations: ['农场', '废弃加油站'] },
            '0,3': { type: 'highway', name: '西环路', locations: ['废弃加油站'] },
            '6,3': { type: 'highway', name: '东环路', locations: ['废弃加油站'] },
            '3,0': { type: 'highway', name: '北环路', locations: ['废弃加油站'] },
            '3,6': { type: 'highway', name: '南环路', locations: ['废弃加油站'] },
            '1,3': { type: 'park', name: '中央公园', locations: ['图书馆'] },
            '5,3': { type: 'university', name: '江南大学', locations: ['图书馆', '科研实验室'] },
            '3,1': { type: 'airport', name: '江南机场', locations: ['军事基地'] }
        };
    }

    generateProvinceMap() {
        return {
            '4,4': { type: 'capital', name: '省会城市', locations: ['医院', '图书馆', '科研实验室'] },
            '3,3': { type: 'city', name: '江南市', locations: ['废弃超市', '汽修厂', '居民区'] },
            '2,2': { type: 'city', name: '青山市', locations: ['农场', '废料场'] },
            '6,2': { type: 'city', name: '东海市', locations: ['太阳能发电站', '化工厂'] },
            '2,6': { type: 'city', name: '南山市', locations: ['军事基地', '核电站'] },
            '1,1': { type: 'mountain', name: '大别山', locations: ['秘密军火库'] },
            '7,1': { type: 'coast', name: '东海岸', locations: ['废弃加油站', '地下避难所'] },
            '1,7': { type: 'desert', name: '南部沙漠', locations: ['核电站'] },
            '7,7': { type: 'forest', name: '原始森林', locations: ['科研实验室'] },
            '0,4': { type: 'highway', name: '西部高速', locations: ['废弃加油站'] },
            '8,4': { type: 'highway', name: '东部高速', locations: ['废弃加油站'] },
            '4,0': { type: 'highway', name: '北部高速', locations: ['废弃加油站'] },
            '4,8': { type: 'highway', name: '南部高速', locations: ['废弃加油站'] },
            '5,5': { type: 'lake', name: '太湖', locations: ['地下避难所'] },
            '3,1': { type: 'industrial', name: '工业带', locations: ['化工厂', '废料场'] }
        };
    }

    generateCountryMap() {
        return {
            '5,5': { type: 'capital', name: '首都', locations: ['医院', '图书馆', '科研实验室', '军事基地'] },
            '4,4': { type: 'province', name: '华东省', locations: ['废弃超市', '汽修厂'] },
            '3,3': { type: 'province', name: '华南省', locations: ['农场', '化工厂'] },
            '6,3': { type: 'province', name: '华北省', locations: ['军事基地', '核电站'] },
            '3,6': { type: 'province', name: '西南省', locations: ['太阳能发电站'] },
            '7,6': { type: 'province', name: '东北省', locations: ['废料场', '秘密军火库'] },
            '2,2': { type: 'mountain', name: '喜马拉雅山', locations: ['秘密军火库'] },
            '8,2': { type: 'coast', name: '东海', locations: ['地下避难所'] },
            '2,8': { type: 'desert', name: '戈壁沙漠', locations: ['核电站'] },
            '8,8': { type: 'forest', name: '大兴安岭', locations: ['科研实验室'] },
            '0,5': { type: 'border', name: '西部边境', locations: ['军事基地'] },
            '10,5': { type: 'border', name: '东部边境', locations: ['军事基地'] },
            '5,0': { type: 'border', name: '北部边境', locations: ['军事基地'] },
            '5,10': { type: 'border', name: '南部边境', locations: ['军事基地'] },
            '1,1': { type: 'special', name: '神秘区域', locations: ['秘密军火库'] }
        };
    }

    getCurrentMapData() {
        return this.mapData[this.gameState.map.currentRegion];
    }

    getLocationKey(x, y) {
        return `${x},${y}`;
    }

    isLocationKnown(x, y) {
        return this.gameState.map.knownLocations.has(this.getLocationKey(x, y));
    }

    isLocationExplored(x, y) {
        return this.gameState.map.exploredLocations.has(this.getLocationKey(x, y));
    }

    revealNearbyLocations(x, y) {
        const directions = [[-1,-1], [-1,0], [-1,1], [0,-1], [0,1], [1,-1], [1,0], [1,1]];
        directions.forEach(([dx, dy]) => {
            const newX = x + dx;
            const newY = y + dy;
            const mapData = this.getCurrentMapData();
            if (newX >= 0 && newX < mapData.size && newY >= 0 && newY < mapData.size) {
                this.gameState.map.knownLocations.add(this.getLocationKey(newX, newY));
            }
        });
    }

    moveToLocation(x, y, locations, addLogEntry, updateUI, updateMapDisplay) {
        const mapData = this.getCurrentMapData();
        const locationKey = this.getLocationKey(x, y);

        if (!this.isLocationKnown(x, y)) {
            addLogEntry("你不知道那个地方在哪里！");
            return false;
        }

        const distance = Math.abs(x - this.gameState.map.currentLocation.x) +
                        Math.abs(y - this.gameState.map.currentLocation.y);
        const fuelCost = distance * mapData.fuelCost;

        if (this.gameState.player.fuel < fuelCost) {
            addLogEntry(`燃料不足！需要${fuelCost}单位燃料，但只有${this.gameState.player.fuel}单位。`);
            return false;
        }

        this.soundManager.play('move');

        // 消耗燃料
        this.gameState.player.fuel = Math.max(0, this.gameState.player.fuel - fuelCost);

        // 更新位置
        this.gameState.map.currentLocation = { x, y };
        this.gameState.map.exploredLocations.add(locationKey);

        // 揭示周围地点
        this.revealNearbyLocations(x, y);

        // 设置当前地点信息
        const locationData = mapData.locations[locationKey];
        if (locationData) {
            const availableLocations = locationData.locations;
            const selectedLocation = availableLocations[Math.floor(Math.random() * availableLocations.length)];

            const locationInfo = locations.find(loc => loc.name === selectedLocation);
            if (locationInfo) {
                this.gameState.location = {
                    name: locationInfo.name,
                    description: locationInfo.description,
                    explored: false,
                    danger: locationInfo.danger,
                    type: locationInfo.type,
                    radiation: locationInfo.radiation || false,
                    mapLocation: locationData.name
                };

                addLogEntry(`到达了${locationData.name}，发现了${selectedLocation}`);
            }
        } else {
            addLogEntry(`到达了未知区域 (${x}, ${y})`);
        }

        updateUI();
        updateMapDisplay();
        return true;
    }

    changeRegion(direction, addLogEntry, updateUI, updateMapDisplay) {
        const regionLevels = ['street', 'village', 'district', 'county', 'city', 'province', 'country'];
        const currentRegion = regionLevels[this.gameState.map.regionLevel];
        const currentMapData = this.mapData[currentRegion];

        let newLevel = this.gameState.map.regionLevel;
        let newLocation = { x: 0, y: 0 };

        if (direction === 'up' && newLevel < 6) {
            // 向上切换到父级区域
            newLevel++;
            const newRegion = regionLevels[newLevel];

            // 根据当前区域在父级区域中的位置设置新位置
            if (currentMapData.parentCoords) {
                newLocation = { ...currentMapData.parentCoords };
            } else {
                // 如果没有父级坐标，默认到中心
                const mapSize = this.mapData[newRegion].size;
                const center = Math.floor(mapSize / 2);
                newLocation = { x: center, y: center };
            }

        } else if (direction === 'down' && newLevel > 0) {
            // 向下切换到子级区域
            newLevel--;
            const newRegion = regionLevels[newLevel];

            // 根据当前位置找到对应的子区域
            const currentLoc = this.gameState.map.currentLocation;
            const currentKey = this.getLocationKey(currentLoc.x, currentLoc.y);

            if (newLevel === 0) { // 切换到县级
                // 从市级切换到县级
                const counties = currentMapData.counties;
                if (counties && counties[currentKey]) {
                    // 进入对应的县，位置设为县中心
                    const mapSize = this.mapData[newRegion].size;
                    const center = Math.floor(mapSize / 2);
                    newLocation = { x: center, y: center };
                    addLogEntry(`进入了${counties[currentKey].name}`);
                } else {
                    addLogEntry("当前位置没有可进入的县！");
                    return;
                }
            } else if (newLevel === 1) { // 切换到市级
                // 从省级切换到市级
                const cities = currentMapData.cities;
                if (cities && cities[currentKey]) {
                    const mapSize = this.mapData[newRegion].size;
                    const center = Math.floor(mapSize / 2);
                    newLocation = { x: center, y: center };
                    addLogEntry(`进入了${cities[currentKey].name}`);
                } else {
                    addLogEntry("当前位置没有可进入的市！");
                    return;
                }
            } else if (newLevel === 2) { // 切换到省级
                // 从国级切换到省级
                const provinces = currentMapData.provinces;
                if (provinces && provinces[currentKey]) {
                    const mapSize = this.mapData[newRegion].size;
                    const center = Math.floor(mapSize / 2);
                    newLocation = { x: center, y: center };
                    addLogEntry(`进入了${provinces[currentKey].name}`);
                } else {
                    addLogEntry("当前位置没有可进入的省！");
                    return;
                }
            }
        } else {
            addLogEntry("无法切换到该区域级别！");
            return;
        }

        const fuelCost = this.mapData[regionLevels[newLevel]].fuelCost * 2;
        if (this.gameState.player.fuel < fuelCost) {
            addLogEntry(`切换区域需要${fuelCost}单位燃料！`);
            return;
        }

        this.soundManager.play('move');
        this.gameState.player.fuel = Math.max(0, this.gameState.player.fuel - fuelCost);
        this.gameState.map.regionLevel = newLevel;
        this.gameState.map.currentRegion = regionLevels[newLevel];
        this.gameState.map.currentLocation = newLocation;

        // 重置已知和探索的位置
        this.gameState.map.exploredLocations = new Set([this.getLocationKey(newLocation.x, newLocation.y)]);
        this.gameState.map.knownLocations = new Set([this.getLocationKey(newLocation.x, newLocation.y)]);
        this.revealNearbyLocations(newLocation.x, newLocation.y);

        addLogEntry(`切换到了${this.mapData[this.gameState.map.currentRegion].name}`);
        updateUI();
        updateMapDisplay();
    }

    getLocationIcon(type) {
        const icons = {
            town: '🏘️',
            village: '🏠',
            highway: '🛣️',
            forest: '🌲',
            mountain: '⛰️',
            lake: '🏞️',
            downtown: '🏙️',
            district: '🏢',
            industrial: '🏭',
            suburb: '🏡',
            park: '🌳',
            university: '🎓',
            airport: '✈️',
            capital: '🏛️',
            city: '🌆',
            coast: '🏖️',
            desert: '🏜️',
            border: '🚧',
            special: '❓'
        };
        return icons[type] || '📍';
    }

    initializeMap(locations) {
        const mapData = this.getCurrentMapData();
        const center = Math.floor(mapData.size / 2);

        this.gameState.map.currentLocation = { x: center, y: center };
        this.gameState.map.exploredLocations = new Set([this.getLocationKey(center, center)]);
        this.gameState.map.knownLocations = new Set([this.getLocationKey(center, center)]);

        this.revealNearbyLocations(center, center);

        const locationData = mapData.locations[this.getLocationKey(center, center)];
        if (locationData && locationData.locations && locationData.locations.length > 0) {
            const selectedLocation = locationData.locations[0];
            const locationInfo = locations.find(loc => loc.name === selectedLocation);
            if (locationInfo) {
                this.gameState.location = {
                    name: locationInfo.name,
                    description: locationInfo.description,
                    explored: false,
                    danger: locationInfo.danger,
                    type: locationInfo.type,
                    radiation: locationInfo.radiation || false,
                    mapLocation: locationData.name
                };
            }
        } else {
            this.gameState.location = {
                name: "废弃加油站",
                description: "一个破败的加油站，可能还有一些有用的物资...",
                explored: false,
                danger: 0.3,
                type: "common",
                radiation: false,
                mapLocation: "青山镇"
            };
        }
    }
}
