// 音效系统模块
export class SoundManager {
    constructor() {
        this.sounds = {};
        this.audioContext = null;
        this.masterVolume = 0.3;
        this.enabled = true;
        this.initAudioContext();
        this.createSounds();
    }

    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API not supported');
            this.enabled = false;
        }
    }

    createSounds() {
        if (!this.enabled) return;

        // 创建各种音效
        this.sounds = {
            explore: this.createTone(220, 0.1, 'sine'),
            combat: this.createTone(110, 0.3, 'sawtooth'),
            upgrade: this.createTone(440, 0.2, 'triangle'),
            move: this.createTone(330, 0.15, 'sine'),
            loot: this.createTone(550, 0.1, 'triangle'),
            damage: this.createTone(80, 0.2, 'sawtooth'),
            heal: this.createTone(660, 0.15, 'sine'),
            death: this.createTone(55, 0.5, 'sawtooth')
        };
    }

    createTone(frequency, duration, type = 'sine') {
        return () => {
            if (!this.enabled || !this.audioContext) return;

            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            gainNode.gain.setValueAtTime(this.masterVolume, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
        };
    }

    play(soundName) {
        if (this.sounds[soundName]) {
            this.sounds[soundName]();
        }
    }

    setVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
    }

    toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }
}
