// 物品系统模块
import { GameData } from './GameData.js';

export class InventorySystem {
    constructor(gameState, soundManager) {
        this.gameState = gameState;
        this.soundManager = soundManager;
        this.itemData = GameData.getItemData();
    }

    getItemName(item) {
        return this.itemData.names[item] || item;
    }

    getItemWeight(item) {
        return this.itemData.weights[item] || 1;
    }

    getItemIcon(item) {
        return this.itemData.icons[item] || '📦';
    }

    getTotalWeight() {
        let totalWeight = 0;
        for (const [item, quantity] of Object.entries(this.gameState.inventory)) {
            totalWeight += quantity * this.getItemWeight(item);
        }
        return totalWeight;
    }

    canAddItem(item, quantity = 1) {
        // 总是允许添加物品，但会影响负重
        return true;
    }

    addItem(item, quantity = 1) {
        // 直接添加物品，不检查重量限制
        this.gameState.inventory[item] = (this.gameState.inventory[item] || 0) + quantity;

        // 更新负重状态
        if (window.game && window.game.weightSystem) {
            window.game.weightSystem.checkOverweight();
        }

        return true;
    }

    removeItem(item, quantity = 1) {
        if ((this.gameState.inventory[item] || 0) < quantity) {
            return false;
        }
        this.gameState.inventory[item] -= quantity;
        return true;
    }

    useItem(item, addLogEntry, updateUI, updateInventory) {
        if ((this.gameState.inventory[item] || 0) <= 0) return;

        switch (item) {
            case 'food':
                this.gameState.inventory.food--;
                this.gameState.player.hunger = Math.min(this.gameState.player.maxHunger,
                    this.gameState.player.hunger + 25);
                this.soundManager.play('heal');
                addLogEntry("使用了食物，饥饿度恢复。");
                break;
            case 'water':
                this.gameState.inventory.water--;
                this.gameState.player.health = Math.min(this.gameState.player.maxHealth,
                    this.gameState.player.health + 15);
                this.soundManager.play('heal');
                addLogEntry("使用了水，生命值恢复。");
                break;
            case 'medicine':
                this.gameState.inventory.medicine--;
                this.gameState.player.health = Math.min(this.gameState.player.maxHealth,
                    this.gameState.player.health + 40);
                this.gameState.player.radiation = Math.max(0, this.gameState.player.radiation - 15);
                this.soundManager.play('heal');
                addLogEntry("使用了药品，大幅恢复生命值并减少了辐射。");
                break;
            case 'fuel':
                this.gameState.inventory.fuel--;
                this.gameState.player.fuel = Math.min(this.gameState.player.maxFuel,
                    this.gameState.player.fuel + 20);
                addLogEntry("为房车加了油。");
                break;
            case 'filters':
                this.gameState.inventory.filters--;
                this.gameState.player.radiation = Math.max(0, this.gameState.player.radiation - 25);
                this.soundManager.play('heal');
                addLogEntry("使用了过滤器，大幅减少了辐射。");
                break;
            case 'books':
                this.gameState.inventory.books--;
                this.gameState.player.morale = Math.min(this.gameState.player.maxMorale,
                    this.gameState.player.morale + 20);
                addLogEntry("阅读了书籍，心情变好了。");
                break;
            case 'seeds':
                if (this.gameState.inventory.water >= 2) {
                    this.gameState.inventory.seeds--;
                    this.gameState.inventory.water -= 2;
                    this.gameState.inventory.food = (this.gameState.inventory.food || 0) + 3;
                    addLogEntry("种植了种子，收获了食物！");
                } else {
                    addLogEntry("种植种子需要2单位水。");
                    return;
                }
                break;
            case 'batteries':
                this.gameState.inventory.batteries--;
                addLogEntry("使用了电池，电子设备暂时恢复了功能。");
                break;
            default:
                addLogEntry(`${this.getItemName(item)}无法直接使用。`);
                return;
        }

        updateUI();
        updateInventory();
    }

    generateLoot(lootTable) {
        const foundItems = [];
        for (const [item, range] of Object.entries(lootTable)) {
            const amount = Math.floor(Math.random() * (range[1] - range[0] + 1)) + range[0];
            if (amount > 0) {
                this.addItem(item, amount);
                foundItems.push(`${this.getItemName(item)} ×${amount}`);
            }
        }
        return foundItems;
    }

    updateInventoryDisplay() {
        const inventoryGrid = document.getElementById('inventory-grid');
        if (!inventoryGrid) return;

        inventoryGrid.innerHTML = '';

        for (const [item, quantity] of Object.entries(this.gameState.inventory)) {
            if (quantity > 0) {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'inventory-item';
                itemDiv.innerHTML = `
                    <div class="item-icon">${this.getItemIcon(item)}</div>
                    <div class="item-name">${this.getItemName(item)}</div>
                    <div class="item-quantity">×${quantity}</div>
                `;
                itemDiv.onclick = () => window.game.useItem(item);
                inventoryGrid.appendChild(itemDiv);
            }
        }

        const totalWeight = this.getTotalWeight();
        const weightElement = document.getElementById('total-weight');
        if (weightElement) {
            weightElement.textContent = `${totalWeight}/${this.gameState.vehicle.maxStorage}`;
        }
    }

    restAtLocation(addLogEntry, updateUI, updateInventory) {
        let rested = false;

        if (this.gameState.inventory.food > 0) {
            this.gameState.inventory.food--;
            this.gameState.player.hunger = Math.min(this.gameState.player.maxHunger,
                this.gameState.player.hunger + 30);
            addLogEntry("吃了一些食物，恢复了体力。");
            rested = true;
        }

        if (this.gameState.inventory.water > 0) {
            this.gameState.inventory.water--;
            this.gameState.player.health = Math.min(this.gameState.player.maxHealth,
                this.gameState.player.health + 10);
            addLogEntry("喝了一些水，感觉好多了。");
            rested = true;
        }

        if (rested) {
            this.soundManager.play('heal');
            this.gameState.player.morale = Math.min(this.gameState.player.maxMorale,
                this.gameState.player.morale + 15);
        } else {
            addLogEntry("没有食物和水可以休息恢复。");
        }

        updateUI();
        updateInventory();
    }
}
