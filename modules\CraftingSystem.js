// 制作系统模块
export class CraftingSystem {
    constructor(gameState, soundManager, inventorySystem) {
        this.gameState = gameState;
        this.soundManager = soundManager;
        this.inventorySystem = inventorySystem;
        this.recipes = this.initializeRecipes();
    }

    initializeRecipes() {
        return {
            // 防具类
            armor: {
                leather_vest: {
                    name: '皮革背心',
                    description: '简单的皮革防护，提供基础防御',
                    materials: { scrap: 3, chemicals: 1 },
                    time: 30,
                    level: 1,
                    stats: { armor: 2, speed: -1 },
                    icon: '🦺'
                },
                metal_armor: {
                    name: '金属护甲',
                    description: '废料制成的金属护甲，防御力强但影响速度',
                    materials: { scrap: 8, rare_metals: 2, tools: 1 },
                    time: 60,
                    level: 2,
                    stats: { armor: 5, speed: -2 },
                    icon: '🛡️'
                },
                tactical_vest: {
                    name: '战术背心',
                    description: '军用战术背心，平衡防护和机动性',
                    materials: { weapon_parts: 3, electronics: 2, chemicals: 2 },
                    time: 45,
                    level: 3,
                    stats: { armor: 4, speed: 0, detection: 1 },
                    icon: '🎽'
                },
                power_armor: {
                    name: '动力装甲',
                    description: '高科技动力装甲，全面提升能力',
                    materials: { rare_metals: 10, electronics: 8, blueprints: 2, batteries: 5 },
                    time: 120,
                    level: 5,
                    stats: { armor: 8, speed: 2, strength: 3 },
                    icon: '🤖'
                }
            },

            // 武器类
            weapons: {
                makeshift_knife: {
                    name: '临时匕首',
                    description: '废料制成的简陋武器',
                    materials: { scrap: 2, tools: 1 },
                    time: 15,
                    level: 1,
                    stats: { attack: 3, speed: 1 },
                    icon: '🔪'
                },
                pipe_gun: {
                    name: '管制枪械',
                    description: '自制的简单火器',
                    materials: { scrap: 5, weapon_parts: 2, chemicals: 1 },
                    time: 45,
                    level: 2,
                    stats: { attack: 8, range: 2 },
                    icon: '🔫'
                },
                crossbow: {
                    name: '十字弩',
                    description: '精准的远程武器',
                    materials: { scrap: 4, weapon_parts: 3, tools: 2 },
                    time: 60,
                    level: 3,
                    stats: { attack: 6, range: 3, accuracy: 2 },
                    icon: '🏹'
                },
                plasma_rifle: {
                    name: '等离子步枪',
                    description: '高能等离子武器',
                    materials: { rare_metals: 8, electronics: 10, blueprints: 3, batteries: 8 },
                    time: 90,
                    level: 5,
                    stats: { attack: 15, range: 4, energy: 1 },
                    icon: '⚡'
                }
            },

            // 工具类
            tools: {
                lockpick_set: {
                    name: '撬锁工具',
                    description: '用于开锁的精密工具',
                    materials: { scrap: 2, electronics: 1 },
                    time: 20,
                    level: 1,
                    stats: { lockpicking: 3 },
                    icon: '🔓'
                },
                scanner: {
                    name: '环境扫描仪',
                    description: '探测周围环境和资源',
                    materials: { electronics: 5, batteries: 3, rare_metals: 1 },
                    time: 40,
                    level: 2,
                    stats: { detection: 4, exploration: 2 },
                    icon: '📡'
                },
                hacking_device: {
                    name: '黑客设备',
                    description: '破解电子系统的工具',
                    materials: { electronics: 8, blueprints: 1, batteries: 4 },
                    time: 50,
                    level: 3,
                    stats: { hacking: 5, electronics_bonus: 2 },
                    icon: '💻'
                },
                multi_tool: {
                    name: '多功能工具',
                    description: '集成多种功能的高级工具',
                    materials: { rare_metals: 5, electronics: 6, tools: 3, blueprints: 1 },
                    time: 70,
                    level: 4,
                    stats: { repair: 3, crafting: 2, versatility: 3 },
                    icon: '🔧'
                }
            },

            // 消耗品类
            consumables: {
                stim_pack: {
                    name: '兴奋剂',
                    description: '临时提升速度和反应',
                    materials: { medicine: 2, chemicals: 3 },
                    time: 10,
                    level: 1,
                    stats: { temp_speed: 5, temp_reaction: 3 },
                    duration: 300, // 5分钟
                    icon: '💉'
                },
                armor_patch: {
                    name: '护甲修补包',
                    description: '快速修复护甲的工具包',
                    materials: { scrap: 1, chemicals: 1 },
                    time: 5,
                    level: 1,
                    stats: { armor_repair: 10 },
                    icon: '🩹'
                },
                energy_cell: {
                    name: '能量电池',
                    description: '为电子设备提供能量',
                    materials: { batteries: 3, rare_metals: 1, chemicals: 2 },
                    time: 15,
                    level: 2,
                    stats: { energy_restore: 50 },
                    icon: '🔋'
                },
                rad_away: {
                    name: '辐射清除剂',
                    description: '强效的辐射清除药剂',
                    materials: { medicine: 3, chemicals: 5, filters: 2 },
                    time: 25,
                    level: 3,
                    stats: { radiation_remove: 50 },
                    icon: '☢️'
                }
            },

            // 辅助装备类
            accessories: {
                tactical_belt: {
                    name: '战术腰带',
                    description: '增加携带能力和快速取用物品',
                    materials: { scrap: 3, weapon_parts: 1, chemicals: 1 },
                    time: 25,
                    level: 1,
                    stats: { carry_capacity: 5, quick_use: 1 },
                    icon: '🎒'
                },
                night_vision: {
                    name: '夜视镜',
                    description: '在夜晚和黑暗环境中提供视野',
                    materials: { electronics: 4, rare_metals: 1, batteries: 2 },
                    time: 35,
                    level: 2,
                    stats: { night_vision: 3, detection: 2 },
                    icon: '🥽'
                },
                gas_mask: {
                    name: '防毒面具',
                    description: '防护有毒气体和辐射',
                    materials: { filters: 3, chemicals: 2, scrap: 2 },
                    time: 30,
                    level: 2,
                    stats: { poison_resist: 4, radiation_resist: 2 },
                    icon: '😷'
                },
                survival_kit: {
                    name: '生存套装',
                    description: '野外生存的综合装备包',
                    materials: { tools: 2, medicine: 3, food: 5, water: 3 },
                    time: 45,
                    level: 3,
                    stats: { survival: 3, health_regen: 1, hunger_efficiency: 1 },
                    icon: '🎯'
                }
            }
        };
    }

    // 检查是否可以制作物品
    canCraft(category, itemId) {
        const recipe = this.recipes[category]?.[itemId];
        if (!recipe) return { canCraft: false, reason: '配方不存在' };

        // 检查制作等级
        const playerLevel = this.gameState.player.level || 1;
        if (playerLevel < recipe.level) {
            return { canCraft: false, reason: `需要等级${recipe.level}` };
        }

        // 检查材料
        for (const [material, required] of Object.entries(recipe.materials)) {
            const available = this.gameState.inventory[material] || 0;
            if (available < required) {
                return {
                    canCraft: false,
                    reason: `缺少${this.inventorySystem.getItemName(material)}(${available}/${required})`
                };
            }
        }

        return { canCraft: true };
    }

    // 制作物品
    craftItem(category, itemId, addLogEntry, updateUI, updateInventory, timeSystem) {
        const recipe = this.recipes[category]?.[itemId];
        if (!recipe) return false;

        const canCraftResult = this.canCraft(category, itemId);
        if (!canCraftResult.canCraft) {
            addLogEntry(`无法制作${recipe.name}: ${canCraftResult.reason}`);
            return false;
        }

        // 消耗材料
        for (const [material, required] of Object.entries(recipe.materials)) {
            this.gameState.inventory[material] -= required;
        }

        // 推进时间
        if (timeSystem) {
            timeSystem.advanceTime(recipe.time);
        }

        // 添加制作的物品到装备或物品栏
        if (category === 'consumables') {
            // 消耗品直接加入物品栏
            this.inventorySystem.addItem(itemId, 1);
        } else {
            // 装备类物品加入装备栏
            this.addEquipment(category, itemId, recipe);
        }

        this.soundManager.play('upgrade');
        addLogEntry(`成功制作了${recipe.name}！`);

        updateUI();
        updateInventory();
        return true;
    }

    // 添加装备
    addEquipment(category, itemId, recipe) {
        if (!this.gameState.equipment) {
            this.gameState.equipment = {
                armor: null,
                weapon: null,
                tool: null,
                accessory: null
            };
        }

        const equipment = {
            id: itemId,
            name: recipe.name,
            description: recipe.description,
            stats: recipe.stats,
            icon: recipe.icon,
            category: category
        };

        if (category === 'armor') {
            this.gameState.equipment.armor = equipment;
        } else if (category === 'weapons') {
            this.gameState.equipment.weapon = equipment;
        } else if (category === 'tools') {
            this.gameState.equipment.tool = equipment;
        } else if (category === 'accessories') {
            this.gameState.equipment.accessory = equipment;
        }
    }

    // 获取所有可制作的物品
    getAllRecipes() {
        const allRecipes = [];
        for (const [category, items] of Object.entries(this.recipes)) {
            for (const [itemId, recipe] of Object.entries(items)) {
                allRecipes.push({
                    category,
                    itemId,
                    ...recipe,
                    canCraft: this.canCraft(category, itemId)
                });
            }
        }
        return allRecipes;
    }

    // 获取装备加成
    getEquipmentBonuses() {
        const bonuses = {
            armor: 0,
            attack: 0,
            speed: 0,
            detection: 0,
            carry_capacity: 0,
            night_vision: 0,
            poison_resist: 0,
            radiation_resist: 0,
            survival: 0,
            health_regen: 0,
            hunger_efficiency: 0,
            quick_use: 0
        };

        if (!this.gameState.equipment) return bonuses;

        // 计算装备加成
        ['armor', 'weapon', 'tool', 'accessory'].forEach(slot => {
            const equipment = this.gameState.equipment[slot];
            if (equipment && equipment.stats) {
                for (const [stat, value] of Object.entries(equipment.stats)) {
                    if (bonuses.hasOwnProperty(stat)) {
                        bonuses[stat] += value;
                    }
                }
            }
        });

        return bonuses;
    }

    // 使用消耗品
    useConsumable(itemId, addLogEntry, updateUI) {
        const item = this.recipes.consumables[itemId];
        if (!item) return false;

        const available = this.gameState.inventory[itemId] || 0;
        if (available <= 0) {
            addLogEntry(`没有${item.name}可以使用`);
            return false;
        }

        // 消耗物品
        this.gameState.inventory[itemId]--;

        // 应用效果
        this.applyConsumableEffects(item, addLogEntry);

        this.soundManager.play('heal');
        addLogEntry(`使用了${item.name}`);
        updateUI();
        return true;
    }

    // 应用消耗品效果
    applyConsumableEffects(item, addLogEntry) {
        const stats = item.stats;

        if (stats.temp_speed) {
            // 临时速度提升
            this.gameState.player.speed = (this.gameState.player.speed || 5) + stats.temp_speed;
            addLogEntry(`速度临时提升${stats.temp_speed}点`);

            // 设置定时器恢复
            setTimeout(() => {
                this.gameState.player.speed -= stats.temp_speed;
            }, (item.duration || 300) * 1000);
        }

        if (stats.armor_repair) {
            // 修复护甲
            addLogEntry(`护甲得到修复`);
        }

        if (stats.energy_restore) {
            // 恢复能量
            addLogEntry(`恢复了能量`);
        }

        if (stats.radiation_remove) {
            // 清除辐射
            this.gameState.player.radiation = Math.max(0,
                this.gameState.player.radiation - stats.radiation_remove);
            addLogEntry(`清除了${stats.radiation_remove}点辐射`);
        }
    }
}
