// 时间系统模块
export class TimeSystem {
    constructor(gameState) {
        this.gameState = gameState;
        this.timeMultiplier = 1; // 时间流逝倍率
    }

    // 推进时间
    advanceTime(minutes) {
        this.gameState.minute += minutes;

        while (this.gameState.minute >= 60) {
            this.gameState.minute -= 60;
            this.gameState.hour += 1;

            if (this.gameState.hour >= 24) {
                this.gameState.hour = 0;
                this.gameState.day += 1;
            }
        }
    }

    // 获取当前时间字符串
    getCurrentTimeString() {
        const hour = this.gameState.hour.toString().padStart(2, '0');
        const minute = this.gameState.minute.toString().padStart(2, '0');
        return `${hour}:${minute}`;
    }

    // 获取时间段描述
    getTimeOfDay() {
        const hour = this.gameState.hour;
        if (hour >= 6 && hour < 12) return '上午';
        if (hour >= 12 && hour < 18) return '下午';
        if (hour >= 18 && hour < 22) return '傍晚';
        return '深夜';
    }

    // 获取时间段的危险修正
    getDangerModifier() {
        const hour = this.gameState.hour;
        if (hour >= 22 || hour < 6) return 1.5; // 深夜更危险
        if (hour >= 18 && hour < 22) return 1.2; // 傍晚稍微危险
        return 1.0; // 白天正常
    }

    // 获取时间段的效率修正
    getEfficiencyModifier() {
        const hour = this.gameState.hour;
        if (hour >= 8 && hour < 18) return 1.0; // 白天正常效率
        if (hour >= 6 && hour < 8) return 0.9; // 清晨稍低
        if (hour >= 18 && hour < 22) return 0.8; // 傍晚较低
        return 0.6; // 深夜很低
    }

    // 检查是否是特殊时间
    isSpecialTime() {
        const hour = this.gameState.hour;
        const minute = this.gameState.minute;

        // 午夜时分 (00:00-01:00)
        if (hour === 0) {
            return { type: 'midnight', description: '午夜时分，神秘事件更容易发生' };
        }

        // 黄金时间 (12:00)
        if (hour === 12 && minute === 0) {
            return { type: 'noon', description: '正午时分，视野最佳' };
        }

        // 黄昏 (18:00-19:00)
        if (hour === 18) {
            return { type: 'dusk', description: '黄昏时分，动物活动频繁' };
        }

        // 黎明 (06:00)
        if (hour === 6 && minute === 0) {
            return { type: 'dawn', description: '黎明时分，新的一天开始' };
        }

        return null;
    }

    // 计算活动所需时间
    calculateActivityTime(baseTime, activityType) {
        let time = baseTime;

        // 时间段修正
        time *= (2 - this.getEfficiencyModifier());

        // 活动类型修正
        switch (activityType) {
            case 'explore':
                // 探索受时间影响较大
                time *= this.gameState.hour >= 22 || this.gameState.hour < 6 ? 2 : 1;
                break;
            case 'combat':
                // 战斗受时间影响较小
                time *= this.gameState.hour >= 22 || this.gameState.hour < 6 ? 1.3 : 1;
                break;
            case 'travel':
                // 旅行受负重影响
                time *= (1 + this.gameState.weightPenalty);
                break;
            case 'rest':
                // 休息在夜晚更有效
                time *= this.gameState.hour >= 22 || this.gameState.hour < 6 ? 0.8 : 1.2;
                break;
        }

        return Math.ceil(time);
    }

    // 获取天气状况（基于时间）
    getWeatherCondition() {
        const hour = this.gameState.hour;
        const day = this.gameState.day;

        // 简单的天气模拟
        const weatherSeed = (day * 24 + hour) % 100;

        if (weatherSeed < 60) {
            return { type: 'clear', description: '晴朗', modifier: 1.0 };
        } else if (weatherSeed < 80) {
            return { type: 'cloudy', description: '多云', modifier: 0.9 };
        } else if (weatherSeed < 90) {
            return { type: 'rain', description: '下雨', modifier: 0.7 };
        } else if (weatherSeed < 95) {
            return { type: 'storm', description: '暴风雨', modifier: 0.5 };
        } else {
            return { type: 'fog', description: '大雾', modifier: 0.6 };
        }
    }

    // 检查是否需要休息
    shouldRest() {
        const hour = this.gameState.hour;
        const fatigue = this.calculateFatigue();

        // 深夜或疲劳度高时建议休息
        return (hour >= 23 || hour < 5) || fatigue > 0.8;
    }

    // 计算疲劳度
    calculateFatigue() {
        const hour = this.gameState.hour;
        const awakeTime = hour >= 6 ? hour - 6 : hour + 18; // 假设6点起床
        return Math.min(1, awakeTime / 18); // 18小时后完全疲劳
    }

    // 获取时间相关的状态效果
    getTimeEffects() {
        const effects = [];
        const hour = this.gameState.hour;
        const fatigue = this.calculateFatigue();
        const weather = this.getWeatherCondition();

        // 疲劳效果
        if (fatigue > 0.6) {
            effects.push({
                type: 'fatigue',
                level: fatigue > 0.8 ? 'high' : 'medium',
                description: fatigue > 0.8 ? '极度疲劳' : '疲劳',
                effect: '降低行动效率'
            });
        }

        // 时间段效果
        if (hour >= 22 || hour < 6) {
            effects.push({
                type: 'darkness',
                level: 'high',
                description: '深夜',
                effect: '增加危险，降低效率'
            });
        }

        // 天气效果
        if (weather.modifier < 1.0) {
            effects.push({
                type: 'weather',
                level: weather.modifier < 0.7 ? 'high' : 'medium',
                description: weather.description,
                effect: '影响行动效率'
            });
        }

        return effects;
    }

    // 自动时间流逝（每10秒游戏时间推进5分钟）
    startTimeFlow() {
        setInterval(() => {
            if (!this.gameState.inCombat) {
                this.advanceTime(5 * this.timeMultiplier);
                // 触发UI更新
                if (window.game && window.game.updateUI) {
                    window.game.updateUI();
                }
            }
        }, 10000);
    }

    // 设置时间流逝倍率
    setTimeMultiplier(multiplier) {
        this.timeMultiplier = multiplier;
    }

    // 获取时间统计
    getTimeStats() {
        const totalMinutes = (this.gameState.day - 1) * 24 * 60 + this.gameState.hour * 60 + this.gameState.minute;
        const totalHours = Math.floor(totalMinutes / 60);

        return {
            totalDays: this.gameState.day,
            totalHours: totalHours,
            totalMinutes: totalMinutes,
            currentTime: this.getCurrentTimeString(),
            timeOfDay: this.getTimeOfDay(),
            fatigue: this.calculateFatigue(),
            weather: this.getWeatherCondition()
        };
    }
}
