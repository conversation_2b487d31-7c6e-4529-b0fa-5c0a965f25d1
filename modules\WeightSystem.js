// 负重系统模块
export class WeightSystem {
    constructor(gameState, soundManager) {
        this.gameState = gameState;
        this.soundManager = soundManager;
    }

    // 计算当前总重量
    getCurrentWeight() {
        let totalWeight = 0;
        const itemWeights = {
            food: 2, water: 3, fuel: 1, scrap: 5, medicine: 1,
            tools: 8, weapon_parts: 6, electronics: 3, chemicals: 4,
            rare_metals: 10, blueprints: 1, batteries: 2, filters: 2,
            seeds: 1, books: 2
        };

        for (const [item, quantity] of Object.entries(this.gameState.inventory)) {
            const weight = itemWeights[item] || 1;
            totalWeight += quantity * weight;
        }

        return totalWeight;
    }

    // 检查是否超重
    checkOverweight() {
        const currentWeight = this.getCurrentWeight();
        const maxWeight = this.gameState.vehicle.maxStorage;
        
        this.gameState.overweight = currentWeight > maxWeight;
        
        if (this.gameState.overweight) {
            // 计算超重惩罚
            const overweightAmount = currentWeight - maxWeight;
            this.gameState.weightPenalty = Math.min(2.0, overweightAmount / maxWeight); // 最大200%惩罚
        } else {
            this.gameState.weightPenalty = 0;
        }

        return this.gameState.overweight;
    }

    // 获取移动速度修正
    getSpeedModifier() {
        if (!this.gameState.overweight) return 1.0;
        
        // 超重会降低移动速度
        const speedReduction = this.gameState.weightPenalty * 0.5; // 最大50%速度降低
        return Math.max(0.3, 1.0 - speedReduction); // 最低保持30%速度
    }

    // 获取燃料消耗修正
    getFuelModifier() {
        if (!this.gameState.overweight) return 1.0;
        
        // 超重会增加燃料消耗
        return 1.0 + this.gameState.weightPenalty; // 最大增加200%燃料消耗
    }

    // 获取时间消耗修正
    getTimeModifier() {
        if (!this.gameState.overweight) return 1.0;
        
        // 超重会增加移动时间
        return 1.0 + this.gameState.weightPenalty * 0.8; // 最大增加160%时间
    }

    // 尝试添加物品
    tryAddItem(item, quantity, itemWeights) {
        const itemWeight = (itemWeights[item] || 1) * quantity;
        const currentWeight = this.getCurrentWeight();
        const newWeight = currentWeight + itemWeight;
        const maxWeight = this.gameState.vehicle.maxStorage;

        // 总是允许添加，但会影响性能
        this.gameState.inventory[item] = (this.gameState.inventory[item] || 0) + quantity;
        
        // 更新超重状态
        this.checkOverweight();
        
        // 如果超重，给出警告
        if (newWeight > maxWeight) {
            const overweightPercent = Math.floor(((newWeight - maxWeight) / maxWeight) * 100);
            return {
                success: true,
                warning: `超重${overweightPercent}%！房车性能下降`,
                overweight: true,
                penalty: this.gameState.weightPenalty
            };
        }

        return { success: true, overweight: false };
    }

    // 获取超重警告信息
    getOverweightWarning() {
        if (!this.gameState.overweight) return null;

        const currentWeight = this.getCurrentWeight();
        const maxWeight = this.gameState.vehicle.maxStorage;
        const overweightPercent = Math.floor(((currentWeight - maxWeight) / maxWeight) * 100);
        const speedPenalty = Math.floor((1 - this.getSpeedModifier()) * 100);
        const fuelPenalty = Math.floor((this.getFuelModifier() - 1) * 100);

        let warningLevel = 'low';
        if (this.gameState.weightPenalty > 0.5) warningLevel = 'medium';
        if (this.gameState.weightPenalty > 1.0) warningLevel = 'high';
        if (this.gameState.weightPenalty > 1.5) warningLevel = 'critical';

        return {
            level: warningLevel,
            overweightPercent: overweightPercent,
            speedPenalty: speedPenalty,
            fuelPenalty: fuelPenalty,
            message: this.getWarningMessage(warningLevel, overweightPercent)
        };
    }

    // 获取警告消息
    getWarningMessage(level, percent) {
        switch (level) {
            case 'low':
                return `轻微超重${percent}%，房车略显沉重`;
            case 'medium':
                return `中度超重${percent}%，房车移动困难`;
            case 'high':
                return `严重超重${percent}%，房车行驶缓慢`;
            case 'critical':
                return `极度超重${percent}%，房车几乎无法移动！`;
            default:
                return `超重${percent}%`;
        }
    }

    // 建议丢弃的物品
    getSuggestions() {
        if (!this.gameState.overweight) return [];

        const suggestions = [];
        const itemValues = {
            // 价值评分：越低越建议丢弃
            scrap: 1, fuel: 2, food: 3, water: 4, medicine: 5,
            tools: 6, weapon_parts: 7, electronics: 8, chemicals: 9,
            rare_metals: 10, blueprints: 10, batteries: 4, filters: 6,
            seeds: 2, books: 3
        };

        const itemWeights = {
            food: 2, water: 3, fuel: 1, scrap: 5, medicine: 1,
            tools: 8, weapon_parts: 6, electronics: 3, chemicals: 4,
            rare_metals: 10, blueprints: 1, batteries: 2, filters: 2,
            seeds: 1, books: 2
        };

        // 计算每个物品的重量/价值比
        for (const [item, quantity] of Object.entries(this.gameState.inventory)) {
            if (quantity > 0) {
                const weight = itemWeights[item] || 1;
                const value = itemValues[item] || 5;
                const ratio = weight / value;
                
                suggestions.push({
                    item: item,
                    quantity: quantity,
                    weight: weight * quantity,
                    value: value,
                    ratio: ratio,
                    suggestion: ratio > 2 ? 'high' : ratio > 1 ? 'medium' : 'low'
                });
            }
        }

        // 按重量/价值比排序，比值高的优先建议丢弃
        return suggestions.sort((a, b) => b.ratio - a.ratio);
    }

    // 自动丢弃低价值物品
    autoDropLowValue(targetWeight) {
        const suggestions = this.getSuggestions();
        let droppedItems = [];
        let currentWeight = this.getCurrentWeight();

        for (const suggestion of suggestions) {
            if (currentWeight <= targetWeight) break;
            if (suggestion.suggestion === 'high') {
                const dropQuantity = Math.min(suggestion.quantity, 
                    Math.ceil((currentWeight - targetWeight) / (suggestion.weight / suggestion.quantity)));
                
                this.gameState.inventory[suggestion.item] -= dropQuantity;
                currentWeight -= (suggestion.weight / suggestion.quantity) * dropQuantity;
                
                droppedItems.push({
                    item: suggestion.item,
                    quantity: dropQuantity
                });
            }
        }

        this.checkOverweight();
        return droppedItems;
    }

    // 获取房车状态描述
    getVehicleCondition() {
        const speedModifier = this.getSpeedModifier();
        const fuelModifier = this.getFuelModifier();

        if (speedModifier >= 0.9) {
            return { status: 'excellent', description: '房车状态良好，运行顺畅' };
        } else if (speedModifier >= 0.7) {
            return { status: 'good', description: '房车略显沉重，但仍可正常行驶' };
        } else if (speedModifier >= 0.5) {
            return { status: 'poor', description: '房车严重超载，行驶困难' };
        } else {
            return { status: 'critical', description: '房车极度超载，几乎无法移动！' };
        }
    }

    // 更新UI显示
    updateWeightDisplay() {
        const currentWeight = this.getCurrentWeight();
        const maxWeight = this.gameState.vehicle.maxStorage;
        const warning = this.getOverweightWarning();
        const condition = this.getVehicleCondition();

        // 更新重量显示
        const weightElement = document.getElementById('total-weight');
        if (weightElement) {
            weightElement.textContent = `${currentWeight}/${maxWeight}kg`;
            
            // 根据超重情况改变颜色
            if (warning) {
                weightElement.style.color = this.getWarningColor(warning.level);
            } else {
                weightElement.style.color = '#e0d5c7';
            }
        }

        // 更新超重警告
        const warningElement = document.getElementById('weight-warning');
        if (warningElement) {
            if (warning) {
                warningElement.textContent = warning.message;
                warningElement.style.display = 'block';
                warningElement.className = `weight-warning ${warning.level}`;
            } else {
                warningElement.style.display = 'none';
            }
        }

        // 更新房车状态
        const conditionElement = document.getElementById('vehicle-condition');
        if (conditionElement) {
            conditionElement.textContent = condition.description;
            conditionElement.className = `vehicle-condition ${condition.status}`;
        }
    }

    // 获取警告颜色
    getWarningColor(level) {
        switch (level) {
            case 'low': return '#ffeb3b';
            case 'medium': return '#ff9800';
            case 'high': return '#f44336';
            case 'critical': return '#d32f2f';
            default: return '#e0d5c7';
        }
    }
}
